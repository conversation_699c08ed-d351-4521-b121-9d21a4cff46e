using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MYOB.AccountRight.SDK;
using MYOB.AccountRight.SDK.Contracts.Version2.Sale;
using MYOB.AccountRight.SDK.Services.Sale;
using MYOB.AccountRight.SDK.Services;
using MYOB.AccountRight.SDK.Contracts.Version2.Contact;
using MYOB.AccountRight.SDK.Contracts.Version2.GeneralLedger;
using MYOB.AccountRight.SDK.Services.Contact;
using MYOB.AccountRight.SDK.Services.GeneralLedger;
using LepInvoicer.Constants;

namespace LepInvoicer.Services;

/// <summary>
/// MYOB integration service implementation
/// </summary>
public class MYOBService : IMYOBService
{
    private readonly ILogger<MYOBService> _logger;
    private readonly InvoicerConfiguration _config;
    private CompanyFile? _companyFile;
    private ICompanyFileCredentials? _credentials;
    private ServiceInvoiceService? _serviceInvoiceService;
    private CustomerService? _customerService;
    private AccountService? _accountService;
    private TaxCodeService? _taxCodeService;
    private EmployeeService? _employeeService;
    private ApiConfiguration? _apiConfiguration;

    // Cached MYOB entities
    private Dictionary<string, Account>? _accounts;
    private TaxCodeLink? _gstTaxCodeLink;
    private AccountLink? _freightAccountLink;
    private AccountLink? _discountsAccountLink;

    public MYOBService(ILogger<MYOBService> logger, IOptions<InvoicerConfiguration> config)
    {
        _logger = logger;
        _config = config.Value;

        // Debug configuration loading
        _logger.LogInformation("MYOBService constructor - Configuration loaded. TestMode: {TestMode}", _config.TestMode);
        _logger.LogInformation("MYOBService constructor - MYOB DeveloperKey: {DeveloperKey}",
            string.IsNullOrEmpty(_config.MYOB?.DeveloperKey) ? "[EMPTY/NULL]" : "[SET]");
    }

    public async Task InitializeAsync()
    {
        _logger.LogInformation("Initializing MYOB service...");

        // Check if test mode is enabled
        if (_config.TestMode)
        {
            _logger.LogWarning("MYOB service running in TEST MODE - MYOB integration disabled");
            return;
        }

        try
        {
            // Log configuration values for debugging
            _logger.LogInformation("MYOB Configuration Debug:");
            _logger.LogInformation("  DeveloperKey: {DeveloperKey}", string.IsNullOrEmpty(_config.MYOB.DeveloperKey) ? "[EMPTY]" : $"[SET - Length: {_config.MYOB.DeveloperKey.Length}]");
            _logger.LogInformation("  DeveloperSecret: {DeveloperSecret}", string.IsNullOrEmpty(_config.MYOB.DeveloperSecret) ? "[EMPTY]" : $"[SET - Length: {_config.MYOB.DeveloperSecret.Length}]");
            _logger.LogInformation("  ConfirmationUrl: '{ConfirmationUrl}'", _config.MYOB.ConfirmationUrl ?? "[NULL]");
            _logger.LogInformation("  CompanyFileName: '{CompanyFileName}'", _config.MYOB.CompanyFileName ?? "[NULL]");
            _logger.LogInformation("  Username: {Username}", string.IsNullOrEmpty(_config.MYOB.Username) ? "[EMPTY]" : "[SET]");
            _logger.LogInformation("  Password: {Password}", string.IsNullOrEmpty(_config.MYOB.Password) ? "[EMPTY]" : "[SET]");

            // Validate configuration
            if (string.IsNullOrEmpty(_config.MYOB.DeveloperKey) ||
                string.IsNullOrEmpty(_config.MYOB.DeveloperSecret) ||
                string.IsNullOrEmpty(_config.MYOB.ConfirmationUrl) ||
                string.IsNullOrEmpty(_config.MYOB.CompanyFileName))
            {
                throw new InvalidOperationException("MYOB configuration is incomplete. Please check appsettings.json");
            }

            // Initialize MYOB API configuration
            _apiConfiguration = new ApiConfiguration(_config.MYOB.DeveloperKey, _config.MYOB.DeveloperSecret, _config.MYOB.ConfirmationUrl);

            // Set security protocol for MYOB API (required for cloud MYOB)
            System.Net.ServicePointManager.SecurityProtocol = System.Net.SecurityProtocolType.Tls12 | System.Net.SecurityProtocolType.Tls11;

            // For MYOB AccountRight cloud, we need to use OAuth authentication
            _logger.LogInformation("Initializing MYOB with OAuth authentication");
            await InitializeWithOAuthAsync();

            // Initialize cached entities
            await InitializeCachedEntitiesAsync();

            _logger.LogInformation("MYOB service initialized successfully with company file: {CompanyFile}", _companyFile.Name);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize MYOB service");
            throw;
        }
    }

    private async Task InitializeWithOAuthAsync()
    {
        try
        {
            _logger.LogInformation("Starting OAuth authentication flow");

            var oauthService = new OAuthService(_apiConfiguration);
            var keystore = new OAuthKeyService(new Microsoft.Extensions.Logging.Abstractions.NullLogger<OAuthKeyService>());
            OAuthTokens tokens;

            if (keystore.OAuthResponse != null)
            {
                tokens = keystore.OAuthResponse;
                _logger.LogInformation("Using existing OAuth tokens");
            }
            else
            {
                _logger.LogInformation("No existing OAuth tokens found, starting OAuth flow");
                var authCode = OAuthLogin.GetAuthorizationCode(_apiConfiguration);
                tokens = await Task.Run(() => oauthService.GetTokens(authCode));
                keystore.OAuthResponse = tokens;
                _logger.LogInformation("OAuth tokens obtained and saved");
            }

            // Initialize services with OAuth keystore
            InitializeServices(_apiConfiguration, null, keystore);

            // Get company file with OAuth credentials
            _logger.LogInformation("Getting company files from MYOB");
            var companyFileService = new CompanyFileService(_apiConfiguration, null, keystore);
            var companyFiles = await Task.Run(() => companyFileService.GetRange());

            _logger.LogInformation("Found {CompanyFileCount} company files", companyFiles.Count());
            foreach (var cf in companyFiles)
            {
                _logger.LogDebug("Company file: {CompanyFileName}", cf.Name);
            }

            _companyFile = companyFiles.FirstOrDefault(cf => cf.Name.Contains(_config.MYOB.CompanyFileName));

            if (_companyFile == null)
            {
                _logger.LogError("MYOB company file '{CompanyFileName}' not found. Available files: {AvailableFiles}",
                    _config.MYOB.CompanyFileName,
                    string.Join(", ", companyFiles.Select(cf => cf.Name)));
                throw new InvalidOperationException($"MYOB company file '{_config.MYOB.CompanyFileName}' not found");
            }

            _logger.LogInformation("MYOB service initialized with OAuth credentials. Using company file: {CompanyFileName}", _companyFile.Name);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize MYOB service with OAuth");
            throw;
        }
    }

    public async Task<bool> CreateOrderInvoiceAsync(IOrder order)
    {
        if (_config.TestMode)
        {
            _logger.LogInformation("TEST MODE: Simulating MYOB invoice creation for order {OrderId}", order.Id);
            await Task.Delay(100); // Simulate some processing time
            return true;
        }

        if (_companyFile == null || _credentials == null || _serviceInvoiceService == null)
            throw new InvalidOperationException("MYOB service not initialized");

        _logger.LogInformation("Creating order invoice for order {OrderId}", order.Id);

        try
        {
            var orderPrice = decimal.Round(order.PriceOfJobs ?? 0, 2);
            var invoiceNumber = $"O{order.Id}";

            // Delete existing invoice if it exists
            DeleteInvoiceInternal(invoiceNumber);

            // Create new service invoice
            var orderInvoice = new ServiceInvoice
            {
                Number = invoiceNumber,
                CustomerPurchaseOrderNumber = InvoicerUtilities.TruncateLongString(order.PurchaseOrder ?? "", 20),
                InvoiceDeliveryStatus = DocumentAction.PrintAndEmail,
                InvoiceType = InvoiceLayoutType.Service,
                Subtotal = orderPrice,
                TotalAmount = orderPrice,
                BalanceDueAmount = orderPrice,
                IsTaxInclusive = false,
                Date = order.FinishDate ?? DateTime.Now,
                Customer = GetCustomerLinkFromOrder(order),
                JournalMemo = $"I2- {order.Customer.Name}",
                Lines = CreateInvoiceLinesFromOrder(order)
            };

            var result = _serviceInvoiceService.InsertEx(_companyFile, orderInvoice, _credentials);

            _logger.LogInformation("Successfully created MYOB invoice {InvoiceNumber} for order {OrderId}",
                invoiceNumber, order.Id);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create MYOB invoice for order {OrderId}", order.Id);
            return false;
        }
    }

    public Task<bool> CreateCreditInvoiceAsync(OrderCredit orderCredit)
    {
        if (_config.TestMode)
        {
            _logger.LogInformation("TEST MODE: Simulating MYOB credit invoice creation for credit {CreditId}", orderCredit.Id);
            return Task.FromResult(true);
        }

        _logger.LogInformation("Creating credit invoice for credit {CreditId}", orderCredit.Id);

        try
        {
            // TODO: Implement actual MYOB credit invoice creation
            // For now, just log and return success
            _logger.LogInformation("Credit invoice created successfully for credit {CreditId}", orderCredit.Id);
            return Task.FromResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create credit invoice for credit {CreditId}", orderCredit.Id);
            return Task.FromResult(false);
        }
    }

    public Task<bool> CreateRefundInvoiceAsync(OrderCredit orderCredit)
    {
        if (_config.TestMode)
        {
            _logger.LogInformation("TEST MODE: Simulating MYOB refund invoice creation for refund {RefundId}", orderCredit.Id);
            return Task.FromResult(true);
        }

        _logger.LogInformation("Creating refund invoice for refund {RefundId}", orderCredit.Id);

        try
        {
            // TODO: Implement actual MYOB refund invoice creation
            // For now, just log and return success
            _logger.LogInformation("Refund invoice created successfully for refund {RefundId}", orderCredit.Id);
            return Task.FromResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create refund invoice for refund {RefundId}", orderCredit.Id);
            return Task.FromResult(false);
        }
    }



    public Task<bool> DeleteInvoiceAsync(string invoiceNumber)
    {
        try
        {
            DeleteInvoiceInternal(invoiceNumber);
            return Task.FromResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete invoice {InvoiceNumber}", invoiceNumber);
            return Task.FromResult(false);
        }
    }

    public AccountLink GetAccountLinkFromJob(IJob job)
    {
        try
        {
            if (_accounts == null)
                throw new InvalidOperationException("MYOB accounts not cached");

            var accountId = InvoicerUtilities.GetAccountDisplayIdFromJob(job);
            _logger.LogDebug("Getting account link for job {JobId}: {AccountId}", job.Id, accountId);

            if (_accounts.TryGetValue(accountId, out var account))
            {
                return new AccountLink { UID = account.UID };
            }

            _logger.LogWarning("Account not found in cached accounts: {AccountId}", accountId);
            // Return a placeholder
            return new AccountLink { UID = Guid.NewGuid() };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting account link for job {JobId}", job.Id);
            // Return a placeholder in case of error
            return new AccountLink { UID = Guid.NewGuid() };
        }
    }

    /// <summary>
    /// Delete existing invoice if it exists
    /// </summary>
    private void DeleteInvoiceInternal(string invoiceNumber)
    {
        try
        {
            if (_serviceInvoiceService == null || _companyFile == null || _credentials == null)
                return;

            var existingInvoices = _serviceInvoiceService.GetRange(_companyFile, null, _credentials,
                $"Number eq '{invoiceNumber}'");

            foreach (var invoice in existingInvoices.Items)
            {
                _serviceInvoiceService.Delete(_companyFile, invoice.UID, _credentials);
                _logger.LogInformation("Deleted existing invoice {InvoiceNumber}", invoiceNumber);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to delete existing invoice {InvoiceNumber}", invoiceNumber);
            // Don't fail the process for deletion issues
        }
    }

    /// <summary>
    /// Get customer link from order
    /// </summary>
    private CustomerLink GetCustomerLinkFromOrder(IOrder order)
    {
        try
        {
            if (_companyFile == null || _credentials == null)
                throw new InvalidOperationException("MYOB service not initialized");

            // Check if we already have the MYOB UID for this customer
            if (!string.IsNullOrEmpty(order.Customer.MyobUid))
            {
                return new CustomerLink { UID = Guid.Parse(order.Customer.MyobUid) };
            }

            // Look up customer by username
            if (_customerService == null)
                throw new InvalidOperationException("Customer service not initialized");

            var userName = System.Net.WebUtility.UrlEncode(order.Customer.Username);
            var custFilter = string.Format(InvoicerConstants.MYOBFilters.CustomerByDisplayIdFilter, userName);

            var customers = _customerService.GetRange(_companyFile, custFilter, _credentials);
            var myobCustomer = customers.Items.FirstOrDefault();

            if (myobCustomer == null)
            {
                _logger.LogWarning("Customer not found in MYOB: {CustomerName} ({Username})",
                    order.Customer.Name, order.Customer.Username);

                // Create the customer in MYOB
                myobCustomer = CreateMYOBCustomerFromLEPCustomer(order.Customer);
                _logger.LogInformation("Created new MYOB customer: {CustomerName} ({Username})",
                    order.Customer.Name, order.Customer.Username);
            }

            return new CustomerLink { UID = myobCustomer.UID };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting customer link for order {OrderId}", order.Id);
            // Return a placeholder in case of error
            return new CustomerLink { UID = Guid.NewGuid() };
        }
    }

    /// <summary>
    /// Create invoice lines from order jobs
    /// </summary>
    private List<ServiceInvoiceLine> CreateInvoiceLinesFromOrder(IOrder order)
    {
        var lines = new List<ServiceInvoiceLine>();

        // Add job lines
        foreach (var job in order.Jobs)
        {
            var line = new ServiceInvoiceLine
            {
                Description = InvoicerUtilities.CreateJobDescription(job),
                Total = decimal.TryParse(job.Price, out var price) ? decimal.Round(price, 2) : 0m,
                Account = GetAccountLinkFromJob(job),
                TaxCode = _gstTaxCodeLink ?? new TaxCodeLink { UID = Guid.NewGuid() },
                UnitCount = 1
            };

            lines.Add(line);
        }

        // Add freight line if applicable
        var freightPrice = decimal.Round(order.PackDetail?.Price ?? 0m, 2);
        if (freightPrice > 0m && _freightAccountLink != null)
        {
            var freightLine = new ServiceInvoiceLine
            {
                Account = _freightAccountLink,
                Description = order.Courier ?? "Freight",
                Total = freightPrice,
                TaxCode = _gstTaxCodeLink ?? new TaxCodeLink { UID = Guid.NewGuid() },
                UnitCount = 1
            };
            lines.Add(freightLine);
        }

        // Add promotion/discount line if applicable
        if (order.Promotion != null && order.PromotionBenefit > 0 && _discountsAccountLink != null)
        {
            var promoLine = new ServiceInvoiceLine
            {
                Account = _discountsAccountLink,
                Description = $"{order.Promotion.PromotionCode} - {order.Promotion.ShortDescription}",
                Total = -1 * decimal.Round(order.PromotionBenefit, 2), // Negative for discount
                TaxCode = _gstTaxCodeLink ?? new TaxCodeLink { UID = Guid.NewGuid() },
                UnitCount = 1
            };
            lines.Add(promoLine);
        }

        return lines;
    }

    /// <summary>
    /// Get GST tax code link
    /// </summary>
    private TaxCodeLink GetGstTaxCodeLink()
    {
        try
        {
            if (_companyFile == null || _credentials == null)
                throw new InvalidOperationException("MYOB service not initialized");

            var taxCodeService = new TaxCodeService(_apiConfiguration);
            var taxCodes = taxCodeService.GetRange(_companyFile, InvoicerConstants.MYOBFilters.GstTaxCodeFilter, _credentials);
            var gstTaxCode = taxCodes.Items.FirstOrDefault();

            if (gstTaxCode == null)
            {
                _logger.LogWarning("GST tax code not found in MYOB");
                // Return a placeholder
                return new TaxCodeLink { UID = Guid.NewGuid() };
            }

            return new TaxCodeLink { UID = gstTaxCode.UID };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting GST tax code");
            // Return a placeholder in case of error
            return new TaxCodeLink { UID = Guid.NewGuid() };
        }
    }

    /// <summary>
    /// Initialize all MYOB services
    /// </summary>
    private void InitializeServices(ApiConfiguration apiConfiguration, ICompanyFileCredentials? credentials, IOAuthKeyService? keystore)
    {
        _serviceInvoiceService = new ServiceInvoiceService(apiConfiguration, null, keystore);
        _customerService = new CustomerService(apiConfiguration, null, keystore);
        _accountService = new AccountService(apiConfiguration, null, keystore);
        _taxCodeService = new TaxCodeService(apiConfiguration, null, keystore);
        _employeeService = new EmployeeService(apiConfiguration, null, keystore);
    }

    /// <summary>
    /// Initialize cached MYOB entities (accounts, tax codes, etc.)
    /// </summary>
    private Task InitializeCachedEntitiesAsync()
    {
        if (_companyFile == null || _accountService == null || _taxCodeService == null)
            return Task.CompletedTask;

        return Task.Run(() =>
        {
            try
            {
                // Cache all accounts
                var accounts = _accountService.GetRange(_companyFile, "", _credentials);
                _accounts = accounts.Items.ToDictionary(a => a.DisplayID);
                _logger.LogInformation("Cached {AccountCount} accounts from MYOB", _accounts.Count);

                // Cache GST tax code
                var gstTaxCode = _taxCodeService.GetRange(_companyFile, InvoicerConstants.MYOBFilters.GstTaxCodeFilter, _credentials);
                var gst = gstTaxCode.Items.FirstOrDefault();
                if (gst != null)
                {
                    _gstTaxCodeLink = new TaxCodeLink { UID = gst.UID };
                    _logger.LogInformation("Cached GST tax code from MYOB");
                }

                // Add delay to respect API limits
                System.Threading.Thread.Sleep(InvoicerConstants.Timing.ApiCallDelayMs);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize cached MYOB entities");
                throw;
            }
        });
    }

    /// <summary>
    /// Create a new MYOB customer from LEP customer data
    /// </summary>
    private Customer CreateMYOBCustomerFromLEPCustomer(ICustomerUser lepCustomer)
    {
        if (_customerService == null || _companyFile == null)
            throw new InvalidOperationException("MYOB service not initialized");

        try
        {
            var myobCustomer = new Customer
            {
                CompanyName = lepCustomer.Name,
                DisplayID = lepCustomer.Username
            };

            // Set up addresses
            var addresses = new List<Address>();

            // Billing address
            var billingAddress = new Address
            {
                Country = lepCustomer.BillingAddress.Country,
                PostCode = lepCustomer.BillingAddress.Postcode,
                State = lepCustomer.BillingAddress.State,
                City = lepCustomer.BillingAddress.City,
                Street = string.Join(", ", new[] {
                    lepCustomer.BillingAddress.Address1,
                    lepCustomer.BillingAddress.Address2,
                    lepCustomer.BillingAddress.Address3
                }.Where(s => !string.IsNullOrEmpty(s))),
                Location = 1 // Billing
            };
            addresses.Add(billingAddress);

            // Shipping address
            var shippingAddress = new Address
            {
                Country = lepCustomer.PostalAddress.Country,
                PostCode = lepCustomer.PostalAddress.Postcode,
                State = lepCustomer.PostalAddress.State,
                City = lepCustomer.PostalAddress.City,
                Street = string.Join(", ", new[] {
                    lepCustomer.PostalAddress.Address1,
                    lepCustomer.PostalAddress.Address2,
                    lepCustomer.PostalAddress.Address3
                }.Where(s => !string.IsNullOrEmpty(s))),
                Location = 2 // Shipping
            };
            addresses.Add(shippingAddress);

            myobCustomer.Addresses = addresses;

            // Set custom fields
            myobCustomer.CustomList2 = new Identifier { Label = "Customer Status", Value = lepCustomer.CustomerStatus };
            myobCustomer.CustomList3 = new Identifier { Label = "Terms", Value = lepCustomer.PaymentTerms.ToString() == "COD" ? "COD/PREPAID" : "ACCOUNT" };

            // Set selling details
            var customerSellingDetails = new CustomerSellingDetails
            {
                TaxCode = _gstTaxCodeLink,
                FreightTaxCode = _gstTaxCodeLink
            };

            // Set sales person if available
            if (!string.IsNullOrEmpty(lepCustomer.SalesConsultant) && _employeeService != null)
            {
                try
                {
                    // This would require a database query to get the sales consultant ID
                    // For now, we'll skip this part as it requires database access
                    _logger.LogDebug("Sales consultant mapping not implemented: {SalesConsultant}", lepCustomer.SalesConsultant);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to set sales person for customer {CustomerName}", lepCustomer.Name);
                }
            }

            myobCustomer.SellingDetails = customerSellingDetails;

            // Create the customer in MYOB
            var result = _customerService.InsertEx(_companyFile, myobCustomer, _credentials);

            // Add delay to respect API limits
            System.Threading.Thread.Sleep(InvoicerConstants.Timing.ApiCallDelayMs);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create MYOB customer for {CustomerName}", lepCustomer.Name);
            throw;
        }
    }
}
