using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MYOB.AccountRight.SDK;
using MYOB.AccountRight.SDK.Contracts;
using MYOB.AccountRight.SDK.Services;
using LepInvoicer.Contracts.Services;

namespace LepInvoicer.Services;

/// <summary>
/// MYOB service implementation for invoice creation and management
/// </summary>
public class MYOBService : IMYOBService
{
    private readonly ILogger<MYOBService> _logger;
    private readonly InvoicerConfiguration _config;
    private readonly IOAuthKeyService _oauthService;
    
    private IApiConfiguration? _apiConfiguration;
    private CompanyFile? _companyFile;
    private ICompanyFileCredentials? _credentials;
    private IServiceInvoiceService? _serviceInvoiceService;
    private ICustomerService? _customerService;
    private TaxCodeLink? _gstTaxCodeLink;
    private Dictionary<string, Account>? _accounts;

    public MYOBService(
        ILogger<MYOBService> logger, 
        IOptions<InvoicerConfiguration> config,
        IOAuthKeyService oauthService)
    {
        _logger = logger;
        _config = config.Value;
        _oauthService = oauthService;
    }

    public async Task Initialize()
    {
        if (_config.TestMode)
        {
            _logger.LogInformation("MYOB Service initialized in TEST MODE - no actual MYOB connection");
            return;
        }

        try
        {
            _logger.LogInformation("Initializing MYOB service...");

            // Initialize API configuration
            _apiConfiguration = new ApiConfiguration(_config.MYOB.ApiBaseUrl);
            
            // Get OAuth credentials
            _credentials = await _oauthService.GetCredentials();
            
            // Initialize services
            var companyFileService = new CompanyFileService(_apiConfiguration);
            _serviceInvoiceService = new ServiceInvoiceService(_apiConfiguration);
            _customerService = new CustomerService(_apiConfiguration);

            // Get company file
            var companyFiles = await Task.Run(() => companyFileService.GetRange(_credentials));
            _companyFile = companyFiles.Items.FirstOrDefault(cf => cf.Name == _config.MYOB.CompanyFileName);
            
            if (_companyFile == null)
                throw new InvalidOperationException($"Company file '{_config.MYOB.CompanyFileName}' not found");

            // Initialize tax codes and accounts
            await InitializeTaxCodesAndAccounts();

            _logger.LogInformation("MYOB service initialized successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize MYOB service");
            throw;
        }
    }

    public async Task<bool> CreateOrderInvoice(IOrder order)
    {
        if (_config.TestMode)
        {
            _logger.LogInformation("TEST MODE: Would create MYOB invoice for order {OrderId}", order.Id);
            await Task.Delay(InvoicerConstants.Timing.TestModeDelayMs);
            return true;
        }

        try
        {
            _logger.LogInformation("Creating MYOB invoice for order {OrderId}", order.Id);

            if (_serviceInvoiceService == null || _companyFile == null || _credentials == null)
                throw new InvalidOperationException("MYOB service not initialized");

            // Generate invoice number
            var invoiceNumber = InvoicerUtilities.GenerateInvoiceNumber("O", order.Id);

            // Delete existing invoice if it exists
            await DeleteExistingInvoice(invoiceNumber);

            // Create service invoice
            var serviceInvoice = new ServiceInvoice
            {
                Number = invoiceNumber,
                Date = DateTime.Now,
                Customer = GetCustomerLinkFromOrder(order),
                IsTaxInclusive = true,
                JournalMemo = "I2",
                Lines = CreateInvoiceLinesFromOrder(order)
            };

            // Create invoice in MYOB
            var response = await Task.Run(() => 
                _serviceInvoiceService.Insert(_companyFile, serviceInvoice, _credentials));

            _logger.LogInformation("Successfully created MYOB invoice {InvoiceNumber} for order {OrderId}", 
                invoiceNumber, order.Id);

            // Add delay between API calls
            await Task.Delay(InvoicerConstants.Timing.ApiCallDelayMs);
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create MYOB invoice for order {OrderId}", order.Id);
            return false;
        }
    }

    public async Task<bool> CreateCreditInvoice(OrderCredit credit)
    {
        if (_config.TestMode)
        {
            _logger.LogInformation("TEST MODE: Would create MYOB credit invoice for credit {CreditId}", credit.Id);
            await Task.Delay(InvoicerConstants.Timing.TestModeDelayMs);
            return true;
        }

        try
        {
            _logger.LogInformation("Creating MYOB credit invoice for credit {CreditId}", credit.Id);

            if (_serviceInvoiceService == null || _companyFile == null || _credentials == null)
                throw new InvalidOperationException("MYOB service not initialized");

            // Generate invoice number
            var invoiceNumber = InvoicerUtilities.GenerateInvoiceNumber(
                credit.Type, credit.Order?.Id, credit.Id);

            // Delete existing invoice if it exists
            await DeleteExistingInvoice(invoiceNumber);

            // Create service invoice
            var serviceInvoice = new ServiceInvoice
            {
                Number = invoiceNumber,
                Date = DateTime.Now,
                Customer = GetCustomerLinkFromOrderCredit(credit),
                IsTaxInclusive = true,
                JournalMemo = "I2"
            };

            // Create invoice line
            var invoiceLine = new ServiceInvoiceLine
            {
                Description = credit.Description,
                Total = credit.Amount,
                TaxCode = _gstTaxCodeLink ?? new TaxCodeLink { UID = Guid.NewGuid() }
            };

            // Set account if specified
            if (!string.IsNullOrEmpty(credit.Account) && _accounts != null && _accounts.ContainsKey(credit.Account))
            {
                var account = _accounts[credit.Account];
                invoiceLine.Account = new AccountLink { UID = account.UID };
            }

            serviceInvoice.Lines = new List<ServiceInvoiceLine> { invoiceLine };

            // Create invoice in MYOB
            var response = await Task.Run(() => 
                _serviceInvoiceService.Insert(_companyFile, serviceInvoice, _credentials));

            _logger.LogInformation("Successfully created MYOB credit invoice {InvoiceNumber} for credit {CreditId}", 
                invoiceNumber, credit.Id);

            // Add delay between API calls
            await Task.Delay(InvoicerConstants.Timing.ApiCallDelayMs);
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create MYOB credit invoice for credit {CreditId}", credit.Id);
            return false;
        }
    }

    public async Task<bool> CreateRefundInvoice(OrderCredit refund)
    {
        if (_config.TestMode)
        {
            _logger.LogInformation("TEST MODE: Would create MYOB refund invoice for refund {RefundId}", refund.Id);
            await Task.Delay(InvoicerConstants.Timing.TestModeDelayMs);
            return true;
        }

        try
        {
            _logger.LogInformation("Creating MYOB refund invoice for refund {RefundId}", refund.Id);

            if (_serviceInvoiceService == null || _companyFile == null || _credentials == null)
                throw new InvalidOperationException("MYOB service not initialized");

            // Generate invoice number
            var invoiceNumber = InvoicerUtilities.GenerateInvoiceNumber(
                refund.Type, customerId: refund.Customer?.Id, creditId: refund.Id);

            // Delete existing invoice if it exists
            await DeleteExistingInvoice(invoiceNumber);

            // Create service invoice
            var serviceInvoice = new ServiceInvoice
            {
                Number = invoiceNumber,
                Date = DateTime.Now,
                Customer = GetCustomerLinkFromCustomer(refund.Customer),
                IsTaxInclusive = true,
                JournalMemo = $"I2 {refund.Customer?.Name}"
            };

            // Create invoice line
            var invoiceLine = new ServiceInvoiceLine
            {
                Description = refund.Description,
                Total = refund.Amount,
                TaxCode = _gstTaxCodeLink ?? new TaxCodeLink { UID = Guid.NewGuid() }
            };

            // Set account if specified
            if (!string.IsNullOrEmpty(refund.Account) && _accounts != null && _accounts.ContainsKey(refund.Account))
            {
                var account = _accounts[refund.Account];
                invoiceLine.Account = new AccountLink { UID = account.UID };
            }

            serviceInvoice.Lines = new List<ServiceInvoiceLine> { invoiceLine };

            // Create invoice in MYOB
            var response = await Task.Run(() => 
                _serviceInvoiceService.Insert(_companyFile, serviceInvoice, _credentials));

            _logger.LogInformation("Successfully created MYOB refund invoice {InvoiceNumber} for refund {RefundId}", 
                invoiceNumber, refund.Id);

            // Add delay between API calls
            await Task.Delay(InvoicerConstants.Timing.ApiCallDelayMs);
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create MYOB refund invoice for refund {RefundId}", refund.Id);
            return false;
        }
    }

    private async Task InitializeTaxCodesAndAccounts()
    {
        // Implementation details for tax codes and accounts initialization
        // This would be moved from the existing MYOBService.cs
        _logger.LogInformation("Initializing tax codes and accounts...");
        
        // TODO: Move the existing initialization logic here
        await Task.CompletedTask;
    }

    private async Task DeleteExistingInvoice(string invoiceNumber)
    {
        // Implementation for deleting existing invoices
        // This would be moved from the existing MYOBService.cs
        await Task.CompletedTask;
    }

    private CustomerLink GetCustomerLinkFromOrder(IOrder order)
    {
        // Implementation for getting customer link from order
        // This would be moved from the existing MYOBService.cs
        return new CustomerLink { UID = Guid.NewGuid() };
    }

    private CustomerLink GetCustomerLinkFromOrderCredit(OrderCredit orderCredit)
    {
        // Implementation for getting customer link from order credit
        // This would be moved from the existing MYOBService.cs
        return new CustomerLink { UID = Guid.NewGuid() };
    }

    private CustomerLink GetCustomerLinkFromCustomer(ICustomerUser customer)
    {
        // Implementation for getting customer link from customer
        // This would be moved from the existing MYOBService.cs
        return new CustomerLink { UID = Guid.NewGuid() };
    }

    private List<ServiceInvoiceLine> CreateInvoiceLinesFromOrder(IOrder order)
    {
        // Implementation for creating invoice lines from order
        // This would be moved from the existing MYOBService.cs
        return new List<ServiceInvoiceLine>();
    }
}
