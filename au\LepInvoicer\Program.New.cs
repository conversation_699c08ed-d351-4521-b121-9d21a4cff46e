using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Serilog;
using NHibernate;
using LepInvoicer.Contracts.Services;
using LepInvoicer.Services;

namespace LepInvoicer;

class Program
{
    static async Task<int> Main(string[] args)
    {
        // Configure Serilog
        Log.Logger = new LoggerConfiguration()
            .WriteTo.Console()
            .WriteTo.File("logs/invoicer-.txt", rollingInterval: RollingInterval.Day)
            .CreateLogger();

        try
        {
            Log.Information("Starting LEP Invoicer application");

            var host = CreateHostBuilder(args).Build();
            var invoicer = host.Services.GetRequiredService<IInvoicerService>();

            var result = await invoicer.RunInvoicer();

            Log.Information("LEP Invoicer completed successfully. Processed {ProcessedCount} items", result);
            return 0;
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "LEP Invoicer terminated unexpectedly");
            return 1;
        }
        finally
        {
            Log.CloseAndFlush();
        }
    }

    static IHostBuilder CreateHostBuilder(string[] args) =>
        Host.CreateDefaultBuilder(args)
            .UseSerilog()
            .ConfigureServices((hostContext, services) =>
            {
                // Configuration
                services.Configure<InvoicerConfiguration>(
                    hostContext.Configuration.GetSection("InvoicerConfiguration"));

                // NHibernate
                services.AddSingleton<ISessionFactory>(provider =>
                {
                    var config = hostContext.Configuration.GetSection("InvoicerConfiguration").Get<InvoicerConfiguration>();
                    return NHibernateHelper.CreateSessionFactory(config.ConnectionString);
                });

                services.AddScoped<ISession>(provider =>
                    provider.GetRequiredService<ISessionFactory>().OpenSession());

                // Services
                services.AddScoped<IDatabaseService, DatabaseService>();
                services.AddScoped<IMYOBService, MYOBService>();
                services.AddScoped<IOAuthKeyService, OAuthKeyService>();
                services.AddScoped<IInvoicerService, InvoicerService>();
            });
}
