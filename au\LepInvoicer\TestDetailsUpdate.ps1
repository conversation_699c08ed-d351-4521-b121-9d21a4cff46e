# Test script to create a sample Invoicer2Log entry with Details
$serverName = "SRV03"
$databaseName = "PRD_AU"
$username = "sa"
$password = "11_Fore5tGl5n"

Write-Host "=== TESTING DETAILS COLUMN UPDATE ===" -ForegroundColor Green

# Create a test entry with Details
$testOrderId = 9999999  # Use a test order ID that won't conflict
$testDetails = "Customer: Test Customer (testuser) | Invoice: O9999999 | PO: TEST-PO-123 | Jobs: 3 | Finished: 2025-06-07"

$insertQuery = @"
INSERT INTO Invoicer2Log ([OrderId], [JobCount], [Total], [FinishDate], [Success], [Details], [DateCreated])
VALUES ($testOrderId, 3, 150.50, '2025-06-07 10:00:00', 'Y', '$testDetails', '2025-06-07 16:45:00')
"@

Write-Host "Creating test entry with Details:" -ForegroundColor Yellow
Write-Host $insertQuery
Write-Host ""

try {
    sqlcmd -S $serverName -d $databaseName -U $username -P $password -Q $insertQuery
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Test entry created successfully!" -ForegroundColor Green
        
        # Now query it back to verify
        $verifyQuery = @"
SELECT 
    OrderId,
    JobCount,
    Total,
    Success,
    CAST(Details AS VARCHAR(200)) AS Details,
    DateCreated
FROM Invoicer2Log 
WHERE OrderId = $testOrderId
"@
        
        Write-Host ""
        Write-Host "Verifying the test entry:" -ForegroundColor Yellow
        sqlcmd -S $serverName -d $databaseName -U $username -P $password -Q $verifyQuery
        
        # Clean up the test entry
        Write-Host ""
        Write-Host "Cleaning up test entry..." -ForegroundColor Yellow
        $deleteQuery = "DELETE FROM Invoicer2Log WHERE OrderId = $testOrderId"
        sqlcmd -S $serverName -d $databaseName -U $username -P $password -Q $deleteQuery
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Test entry cleaned up successfully!" -ForegroundColor Green
        }
        
    } else {
        Write-Host "❌ Failed to create test entry" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== CHECKING RECENT ENTRIES ===" -ForegroundColor Green

# Check the most recent entries to see the difference
$recentQuery = @"
SELECT TOP 5
    OrderId,
    Success,
    CASE 
        WHEN Details IS NULL THEN '[NULL - OLD STYLE]'
        WHEN CAST(Details AS VARCHAR(MAX)) = '' THEN '[EMPTY STRING]'
        ELSE LEFT(CAST(Details AS VARCHAR(MAX)), 100) + CASE WHEN LEN(CAST(Details AS VARCHAR(MAX))) > 100 THEN '...' ELSE '' END
    END AS Details_Sample,
    DateCreated
FROM Invoicer2Log 
ORDER BY DateCreated DESC
"@

Write-Host "Recent entries comparison:" -ForegroundColor Yellow
sqlcmd -S $serverName -d $databaseName -U $username -P $password -Q $recentQuery

Write-Host ""
Write-Host "=== TEST COMPLETE ===" -ForegroundColor Green
Write-Host "✅ The Details column can now store useful information for successful orders!" -ForegroundColor Green
