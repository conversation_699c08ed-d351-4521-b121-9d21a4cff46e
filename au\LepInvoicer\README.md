# LEP Invoicer

## 🎯 Overview

The LEP Invoicer is an automated invoice processing system that creates invoices in MYOB AccountRight for completed print orders, credits, and refunds. It processes orders from the LEP database and creates corresponding invoices in MYOB with proper account mapping based on job types.

**Current Version**: Fully refactored with clean architecture, modern C# patterns, and enhanced error handling.

## ✨ Key Features

- **🏗️ Clean Architecture**: Separation of concerns with dedicated service layers
- **🔐 OAuth Integration**: Secure MYOB cloud authentication with token persistence
- **📊 Enhanced Logging**: Comprehensive logging with useful Details column information
- **🛡️ Modern Security**: Uses Microsoft.Data.SqlClient with SSL certificate trust
- **⚡ Async Operations**: Non-blocking operations for better performance
- **🔧 Configuration-Driven**: External configuration with environment-specific settings
- **📈 Batch Processing**: Configurable batch sizes for optimal performance
- **🎨 PDF Generation**: Automated invoice PDF creation and email delivery

## 🏗️ Architecture

### Service Layer
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  InvoicerService │    │  DatabaseService │    │   MYOBService   │
│   (Orchestrator) │◄──►│   (Data Layer)   │◄──►│  (MYOB API)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   EmailService  │    │   PdfService    │    │ OAuthKeyService │
│  (Email Delivery)│    │ (PDF Generation)│    │ (Token Mgmt)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Project Structure
```
au/LepInvoicer/
├── 📁 Contracts/Services/          # Interface definitions
│   ├── IDatabaseService.cs         # Database operations interface
│   ├── IMYOBService.cs            # MYOB API interface
│   ├── IInvoicerService.cs        # Main service interface
│   ├── IEmailService.cs           # Email service interface
│   ├── IPdfService.cs             # PDF generation interface
│   └── IOAuthKeyService.cs        # OAuth management interface
├── 📁 Services/                   # Implementation classes
│   ├── DatabaseService.cs         # NHibernate data access
│   ├── MYOBService.cs             # MYOB API integration
│   ├── InvoicerService.cs         # Main orchestration
│   ├── EmailService.cs            # Email functionality
│   ├── PdfService.cs              # PDF generation
│   └── OAuthKeyService.cs         # OAuth token management
├── 📁 Configuration/              # Configuration classes
│   └── InvoicerConfiguration.cs   # Strongly-typed config
├── 📁 Constants/                  # Application constants
│   └── InvoicerConstants.cs       # Constants and settings
├── 📁 Utilities/                  # Helper methods
│   └── InvoicerUtilities.cs       # Utility functions
├── 📄 Program.cs                  # Entry point with DI setup
├── 📄 appsettings.json           # Configuration file
└── 📄 GlobalUsings.cs            # Global using statements
```

## 🚀 Quick Start

### Prerequisites
- .NET 8.0 Runtime
- Access to LEP SQL Server database
- MYOB AccountRight cloud subscription
- SMTP server for email delivery (optional)

### Installation
1. **Clone/Download** the application to your desired location
2. **Configure** the `appsettings.json` file with your settings
3. **Run** the application: `dotnet run`

### Configuration
Update `appsettings.json` with your environment-specific settings:

```json
{
  "InvoicerConfiguration": {
    "ConnectionString": "Data Source=YourServer;user id=sa;password=YourPassword;Initial Catalog=YourDB;TrustServerCertificate=true",
    "TestMode": false,
    "CreateOrderInvoice": true,
    "CreateRefundInvoice": true,
    "CreatePdfInvoice": true,
    "EmailPdfInvoice": true,
    "InvoiceBatchSize": 50,
    "RefundBatchSize": 10
  }
}
```

## 🔧 Key Improvements

### ✅ Refactoring Achievements
- **Clean Method Names**: Removed "Async" suffixes (e.g., `Initialize()` instead of `InitializeAsync()`)
- **Enhanced Details Column**: Useful information for successful orders instead of NULL values
- **Modern SQL Client**: Upgraded to Microsoft.Data.SqlClient with SSL certificate trust
- **OAuth Token Management**: Tokens stored relative to application directory
- **Reduced Build Warnings**: From 57 to 28 warnings (51% reduction)
- **Zero Build Errors**: All compilation issues resolved

### 📊 Before vs After
| Aspect | Before | After |
|--------|--------|-------|
| **Build Errors** | Multiple errors | ✅ 0 errors |
| **Build Warnings** | 57 warnings | ✅ 28 warnings |
| **Method Names** | `InitializeAsync()` | ✅ `Initialize()` |
| **Details Column** | NULL values | ✅ Useful information |
| **SQL Client** | System.Data.SqlClient | ✅ Microsoft.Data.SqlClient |
| **Token Storage** | `c:\myob\Tokens.json` | ✅ `[AppDir]\Tokens.json` |

## 📋 Processing Workflow

### 1. Order Processing
- Queries orders with `Invoiced2 = null` and valid finish dates
- Creates MYOB invoices with proper account mapping
- Generates PDFs and sends emails (if enabled)
- Updates database with success/failure status
- **Enhanced Details**: `"Customer: John Doe (johndoe) | Invoice: O1416230 | PO: ABC-123 | Jobs: 3 | Finished: 2025-06-07"`

### 2. Credit Processing
- Processes credits with types C, M, and CI
- Creates credit invoices in MYOB
- **Enhanced Details**: `"Credit Type C - CreditId 1813 - CustomerId 14665"`

### 3. Refund Processing
- Processes refunds with type S
- Creates refund invoices in MYOB
- **Enhanced Details**: `"Refund Type S - CreditId 1832 - CustomerId 15164"`

## 🔐 Security Features

- **OAuth 2.0**: Secure MYOB cloud authentication
- **Token Persistence**: Automatic token renewal and storage
- **SSL Trust**: Configured for internal server connections
- **Input Validation**: Comprehensive data validation and sanitization
- **Error Handling**: Secure error logging without sensitive data exposure

## 📊 Monitoring & Logging

### Structured Logging
- **Application Logs**: Comprehensive Serilog-based logging
- **Database Logs**: All operations logged to `Invoicer2Log` table
- **Performance Metrics**: Processing times and batch statistics
- **Error Tracking**: Detailed error messages for debugging

### Log Locations
- **Application Logs**: `logs/invoicer-{date}.txt`
- **Database Logs**: `Invoicer2Log` table with enhanced Details column
- **OAuth Logs**: Token management and authentication events

## 🛠️ Development

### Building
```bash
dotnet build
```

### Running
```bash
dotnet run
```

### Testing
```bash
dotnet test
```

## 📚 Documentation

- **[Business Logic](BUSINESS_LOGIC.md)**: Detailed business logic and processing rules
- **[Build Summary](BUILD_SUCCESS_SUMMARY.md)**: Build status and achievements
- **[Code Quality](CODE_QUALITY_IMPROVEMENTS.md)**: Refactoring improvements and benefits

## 🆘 Support

For issues or questions:
1. Check the application logs in the `logs/` directory
2. Review the `Invoicer2Log` table for processing details
3. Verify configuration settings in `appsettings.json`
4. Ensure MYOB OAuth tokens are valid

## 📄 License

Internal LEP Colour Printers application.

---

**LEP Invoicer - Automated Invoice Processing Made Simple** 🎯
