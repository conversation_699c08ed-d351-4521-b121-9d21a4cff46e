# LEP Invoicer Business Logic Documentation

## Overview
The LEP Invoicer is an automated invoice processing system that creates invoices in MYOB AccountRight for completed print orders, credits, and refunds. It processes orders from the LEP database and creates corresponding invoices in MYOB with proper account mapping based on job types.

## Main Processing Workflow

### 1. Application Startup
1. **Database Cleanup**: Removes failed invoice attempts and duplicate log entries
2. **Service Initialization**: 
   - Database connection to LEP SQL Server
   - MYOB OAuth authentication and company file connection
   - Account and tax code caching from MYOB
3. **Processing Execution**: Processes orders, credits, and refunds in sequence

### 2. Order Invoice Processing

#### Query Criteria for Orders to Invoice
```sql
-- From DatabaseService.GetOrdersToInvoiceAsync()
session.Query<Order>()
    .Where(o => o.Invoiced2 == null)                    // Not already invoiced
    .Where(o => o.FinishDate != null)                   // Has finish date
    .Where(o => o.FinishDate >= MinimumFinishDate)      // After 2024/02/01
    .Where(o => o.PriceOfJobs > 0)                      // Has positive price
    .Where(o => !IgnoreCustomers.Contains(o.Customer.CompanyName)) // Not ignored customer
    .OrderBy(o => o.FinishDate)                         // Process oldest first
    .Take(InvoiceBatchSize)                             // Batch processing (default: 50)
```

#### Order Validation Rules
- **Price Check**: Order must have `PriceOfJobs > 0`
- **Finish Date**: Order must have a valid `FinishDate`
- **Jobs**: Order must contain at least one job
- **Customer**: Customer must not be in ignore list

#### Order Processing Steps
1. **MYOB Invoice Creation**: Creates service invoice in MYOB with:
   - Customer lookup by DisplayID
   - Job lines with account mapping
   - Freight charges (if applicable)
   - Discount lines (if applicable)
   - GST tax code application

2. **PDF Generation** (if enabled):
   - Uses FastReport template: `C:\LepData\Labels2\lep-invoice-order.frx`
   - Saves to: `{PdfFolder}/{yyyy/MMM/dd}/O{OrderId}.pdf`
   - Copies to order folder: `{DataDirectory}/orders/{yyyyMMdd}/{OrderNr}/Extrafiles/`

3. **Email Delivery** (if enabled):
   - Sends PDF to customer's account email or regular email
   - Subject: "Invoice O{OrderId}"
   - From: <EMAIL>

4. **Database Updates**:
   - Marks order as invoiced: `Invoiced2 = 'Y'`
   - Logs processing result in `Invoicer2Log` table

### 3. Credit Invoice Processing

#### Query Criteria for Credits to Invoice
```sql
-- From DatabaseService.GetCreditsToInvoiceAsync()
session.Query<OrderCredit>()
    .Where(c => c.Invoiced != "Y")                      // Not already invoiced
    .Where(c => c.Type == "C" || c.Type == "M" || c.Type == "CI") // Credit types: C, M, CI
    .Where(c => c.Order != null)                        // Has associated order
    .Where(c => !IgnoreCustomers.Contains(c.Order.Customer.Name)) // Not ignored customer
    .OrderBy(c => c.DateCreated)                        // Process oldest first
    .Take(RefundBatchSize)                              // Batch processing (default: 50)
```

#### Credit Type Definitions
- **Type "C"**: Standard credits
- **Type "M"**: Miscellaneous credits
- **Type "CI"**: Credit invoices (your specific case)

**Note**: All three types are now properly processed by the system.

#### Credit Processing Steps
1. **Invoice Number Generation**: Creates invoice number using pattern `{Type}{OrderId}{CreditId}`
   - Type "C" = Credit: `C{OrderId}{CreditId}` (e.g., C123456789)
   - Type "M" = Miscellaneous: `M{OrderId}{CreditId}` (e.g., M123456789)

2. **Existing Invoice Cleanup**: Deletes any existing MYOB invoice with same number

3. **MYOB Credit Invoice Creation**: Creates service invoice in MYOB with:
   - Customer lookup from associated order
   - Single line item with credit description and amount
   - Account mapping from `OrderCredit.Account` field (if specified)
   - GST tax code application
   - Journal memo: "I2"

4. **Invoicer2Log Entry**: Logs success/failure to Invoicer2Log table with:
   - OrderId: Associated order ID
   - JobCount: 1 (fixed for credits)
   - Total: Credit amount
   - FinishDate: Credit creation date
   - Success: 'Y' or 'N'
   - Details: Error message if failed

5. **Database Update**: Marks credit as invoiced: `Invoiced = 'Y'`

### 4. Refund Invoice Processing

#### Query Criteria for Refunds to Invoice
```sql
-- From DatabaseService.GetRefundsToInvoiceAsync()
session.Query<OrderCredit>()
    .Where(r => r.Invoiced != "Y")                      // Not already invoiced
    .Where(r => r.Type == "S")                          // Refund type only
    .Where(r => r.Customer != null)                     // Has customer
    .Where(r => !IgnoreCustomers.Contains(r.Customer.CompanyName)) // Not ignored customer
    .OrderBy(r => r.DateCreated)                        // Process oldest first
    .Take(RefundBatchSize)                              // Batch processing (default: 50)
```

#### Refund Processing Steps
1. **Invoice Number Generation**: Creates invoice number using pattern `S{CustomerId}{CreditId}`
   - Example: `S12345678` (Customer ID 1234, Credit ID 5678)

2. **Existing Invoice Cleanup**: Deletes any existing MYOB invoice with same number

3. **MYOB Refund Invoice Creation**: Creates service invoice in MYOB with:
   - Customer lookup by DisplayID or creation if not found
   - Single line item with refund description and amount
   - Account mapping from `OrderCredit.Account` field (if specified)
   - GST tax code application
   - Journal memo: "I2 {CustomerName}"

4. **Invoicer2Log Entry**: Logs success/failure to Invoicer2Log table with:
   - OrderId: NULL (refunds don't have associated orders)
   - JobCount: 1 (fixed for refunds)
   - Total: Refund amount
   - FinishDate: Refund creation date
   - Success: 'Y' or 'N'
   - Details: Includes CustomerId for tracking

5. **Database Update**: Marks refund as invoiced: `Invoiced = 'Y'`

## Account Mapping Logic

### Job Type to MYOB Account Mapping
The system maps print jobs to specific MYOB accounts based on job characteristics:

#### Offset Printing Accounts (PrintType = 'O')
- **Business Cards**: `4-1004` (Offset Business Cards)
- **Brochures**: `4-1005` (Offset Brochures)  
- **Stationery**: `4-1006` (Offset Stationery)
- **Magazines**: `4-1007` (Offset Magazines)
- **Presentation Folders**: `4-1008` (Offset Presentation Folders)
- **NCR Books/Envelopes**: `4-1009` (Offset Other)
- **Default**: `4-1009` (Offset Other)

#### Digital Printing Accounts (PrintType != 'O')
- **Business Cards**: `4-2004` (Digital Business Cards)
- **Brochures**: `4-2005` (Digital Brochures)
- **Stationery**: `4-2006` (Digital Stationery)
- **Magazines**: `4-2007` (Digital Magazines)
- **Presentation Folders**: `4-2008` (Digital Presentation Folders)
- **Default**: `4-2009` (Digital Other)

#### Wide Format Accounts
- **Adhesive Signs**: `4-3020` (Wide Format Adhesives)
- **Banners/Pull Ups**: `4-3021` (Wide Format Banners)

#### Special Accounts
- **Freight**: `8-1050` (Freight Recovered)
- **Discounts**: `4-1010` (Discounts Allowed)

### Job Classification Logic
Jobs are classified based on template categories and characteristics:
- **Business Cards**: Template name contains "business card" patterns
- **Brochures**: Template name contains "brochure", "flyer", "leaflet" patterns
- **Magazines**: Template name contains "magazine", "booklet" patterns
- **Stationery**: Template category contains "Stationery"
- **Presentation Folders**: Template category contains "Presentation Folders"
- **Adhesive Signs**: Template category contains "Adhesive Signs, Rigid Signs & Stickers"
- **Banners**: Template category contains "Banners/Pull Ups"
- **NCR Books**: Template name contains "ncr" patterns
- **Envelopes**: Template name contains "envelope" patterns

## Configuration Settings

### Processing Controls
- **CreateOrderInvoice**: Enable/disable order invoice processing
- **CreateRefundInvoice**: Enable/disable credit/refund processing
- **CreatePdfInvoice**: Enable/disable PDF generation
- **EmailPdfInvoice**: Enable/disable email delivery
- **TestMode**: Bypass MYOB integration for testing

### Batch Sizes
- **InvoiceBatchSize**: Orders processed per run (default: 50)
- **RefundBatchSize**: Credits/refunds processed per run (default: 50)

### Customer Filtering
**IgnoreCustomers**: List of customers to skip during processing:
- "LEP Colour Printers Pty Ltd"
- "LEP Marketing"
- "LEP TEST J"
- "LEP TEST T"
- "lepdemo"

### Date Filtering
- **MinimumFinishDate**: Only process orders finished after 2024/02/01

## Error Handling

### Order Processing Errors
- **Zero Price**: Order marked as invoiced but logged as validation failure
- **Missing Finish Date**: Order skipped with warning
- **MYOB Errors**: Order marked as failed with error details
- **PDF Errors**: Logged but don't fail the order
- **Email Errors**: Logged but don't fail the order

### Database Error Handling
- **Connection Issues**: Application fails with detailed logging
- **Query Errors**: Individual items skipped with error logging
- **Update Errors**: Logged with retry logic where applicable

### MYOB Error Handling
- **Authentication Errors**: Application fails during initialization
- **API Errors**: Individual invoices fail with detailed error logging
- **Rate Limiting**: Built-in delays between API calls (150ms)

## Logging and Auditing

### Processing Logs
All processing results are logged to `Invoicer2Log` table:
- OrderId, JobCount, Total, FinishDate
- Success/Failure flag and error details
- Processing timestamp

### Application Logs
Comprehensive logging using Serilog:
- Startup and initialization
- Processing progress and results
- Error details and stack traces
- Performance metrics

### Database Status Updates
- Orders: `Invoiced2` field ('Y' = success, 'F' = failed, null = pending)
- Credits/Refunds: `Invoiced` field ('Y' = invoiced, null = pending)
- Error details stored in `Invoiced2Details` field for failed orders
