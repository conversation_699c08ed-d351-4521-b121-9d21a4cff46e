namespace LepInvoicer.Contracts.Services;

/// <summary>
/// MYOB service interface for invoice creation and management
/// </summary>
public interface IMYOBService
{
    /// <summary>
    /// Initialize MYOB service with authentication and company file
    /// </summary>
    Task Initialize();

    /// <summary>
    /// Create an invoice in MYOB for the given order
    /// </summary>
    Task<bool> CreateOrderInvoice(IOrder order);

    /// <summary>
    /// Create a credit invoice in MYOB for the given credit
    /// </summary>
    Task<bool> CreateCreditInvoice(OrderCredit credit);

    /// <summary>
    /// Create a refund invoice in MYOB for the given refund
    /// </summary>
    Task<bool> CreateRefundInvoice(OrderCredit refund);
}
