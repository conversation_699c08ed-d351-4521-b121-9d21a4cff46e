using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Net.Mail;
using LepInvoicer.Contracts.Services;

namespace LepInvoicer.Services;

/// <summary>
/// Email service implementation
/// </summary>
public class EmailService : IEmailService
{
    private readonly ILogger<EmailService> _logger;
    private readonly InvoicerConfiguration _config;

    public EmailService(ILogger<EmailService> logger, IOptions<InvoicerConfiguration> config)
    {
        _logger = logger;
        _config = config.Value;
    }

    public async Task<bool> SendInvoiceEmail(string toAddress, string invoiceNumber, string pdfFilePath)
    {
        if (string.IsNullOrEmpty(toAddress))
        {
            _logger.LogWarning("Cannot send email - no recipient address provided");
            return false;
        }

        if (!File.Exists(pdfFilePath))
        {
            _logger.LogWarning("Cannot send email - PDF file not found: {PdfPath}", pdfFilePath);
            return false;
        }

        try
        {
            _logger.LogInformation("Sending invoice email to {Email} for invoice {InvoiceNumber}", toAddress, invoiceNumber);

            using var smtpClient = new SmtpClient(_config.Smtp.Host, _config.Smtp.Port)
            {
                EnableSsl = _config.Smtp.EnableSsl,
                Credentials = new System.Net.NetworkCredential(_config.Smtp.Username, _config.Smtp.Password)
            };

            using var mail = CreateEmail();
            mail.To.Add(new MailAddress(toAddress));
            mail.Subject = string.Format(InvoicerConstants.Email.InvoiceSubjectTemplate, invoiceNumber);
            mail.Body = string.Format(InvoicerConstants.Email.InvoiceBodyTemplate, invoiceNumber);
            mail.Attachments.Add(new Attachment(pdfFilePath));

            await smtpClient.SendMailAsync(mail);

            _logger.LogInformation("Email sent successfully to {Email} for invoice {InvoiceNumber}", toAddress, invoiceNumber);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send email to {Email} for invoice {InvoiceNumber}", toAddress, invoiceNumber);
            return false;
        }
    }

    public MailMessage CreateEmail()
    {
        var mail = new MailMessage
        {
            From = new MailAddress(_config.Smtp.FromAddress, _config.Smtp.FromName),
            IsBodyHtml = true
        };

        return mail;
    }
}
