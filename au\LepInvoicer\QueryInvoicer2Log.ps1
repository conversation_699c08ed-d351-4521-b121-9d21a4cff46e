# PowerShell script to query Invoicer2Log table
# Usage: .\QueryInvoicer2Log.ps1

$serverName = "SRV03"
$databaseName = "PRD_AU"
$username = "sa"
$password = "11_Fore5tGl5n"

# Order IDs to investigate
$orderIds = @(1416230, 1416418, 1416327, 1415849, 1416128, 1415366)

Write-Host "=== QUERYING INVOICER2LOG FOR SPECIFIC ORDERS ===" -ForegroundColor Green
Write-Host ""

# Build the SQL query
$orderIdList = $orderIds -join ","
$query = @"
SELECT
    OrderId,
    JobCount,
    Total,
    FinishDate,
    Success,
    CASE
        WHEN Details IS NULL THEN '[NULL]'
        WHEN CAST(Details AS VARCHAR(MAX)) = '' THEN '[EMPTY STRING]'
        ELSE CAST(Details AS VARCHAR(100))
    END AS Details,
    DateCreated
FROM Invoicer2Log
WHERE OrderId IN ($orderIdList)
ORDER BY OrderId, DateCreated DESC
"@

Write-Host "Query:" -ForegroundColor Yellow
Write-Host $query
Write-Host ""

try {
    # Execute the query using sqlcmd
    $results = sqlcmd -S $serverName -d $databaseName -U $username -P $password -Q $query -h -1 -s "|"
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Results:" -ForegroundColor Green
        Write-Host "OrderId|JobCount|Total|FinishDate|Success|Details|DateCreated" -ForegroundColor Cyan
        Write-Host "-------|--------|-----|----------|-------|-------|----------" -ForegroundColor Cyan
        
        foreach ($line in $results) {
            if ($line -and $line.Trim() -ne "") {
                Write-Host $line
            }
        }
    } else {
        Write-Host "Error executing query. Exit code: $LASTEXITCODE" -ForegroundColor Red
    }
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== ADDITIONAL ANALYSIS ===" -ForegroundColor Green

# Check if these orders exist in the Order table
$orderCheckQuery = @"
SELECT
    o.Id as OrderId,
    o.DateCreated as OrderDate,
    c.Name as CustomerName,
    o.Total as OrderTotal,
    CASE WHEN il.OrderId IS NOT NULL THEN 'YES' ELSE 'NO' END as HasInvoicer2LogEntry
FROM [Order] o
LEFT JOIN Customer c ON o.CustomerId = c.Id
LEFT JOIN Invoicer2Log il ON o.Id = il.OrderId
WHERE o.Id IN ($orderIdList)
ORDER BY o.Id
"@

Write-Host ""
Write-Host "Checking if these orders exist:" -ForegroundColor Yellow
Write-Host $orderCheckQuery
Write-Host ""

try {
    $orderResults = sqlcmd -S $serverName -d $databaseName -U $username -P $password -Q $orderCheckQuery -h -1 -s "|"
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Order Analysis:" -ForegroundColor Green
        Write-Host "OrderId|OrderDate|CustomerName|OrderTotal|HasInvoicer2LogEntry" -ForegroundColor Cyan
        Write-Host "-------|---------|------------|----------|-------------------" -ForegroundColor Cyan
        
        foreach ($line in $orderResults) {
            if ($line -and $line.Trim() -ne "") {
                Write-Host $line
            }
        }
    }
} catch {
    Write-Host "Error in order analysis: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== DETAILS COLUMN ANALYSIS ===" -ForegroundColor Green

# Check the pattern of Details column across all recent entries
$detailsAnalysisQuery = @"
SELECT TOP 20
    OrderId,
    Success,
    CASE
        WHEN Details IS NULL THEN '[NULL]'
        WHEN CAST(Details AS VARCHAR(MAX)) = '' THEN '[EMPTY STRING]'
        WHEN LEN(CAST(Details AS VARCHAR(MAX))) = 0 THEN '[ZERO LENGTH]'
        ELSE LEFT(CAST(Details AS VARCHAR(MAX)), 50) + CASE WHEN LEN(CAST(Details AS VARCHAR(MAX))) > 50 THEN '...' ELSE '' END
    END AS Details_Sample,
    LEN(ISNULL(CAST(Details AS VARCHAR(MAX)), '')) as Details_Length,
    DateCreated
FROM Invoicer2Log
ORDER BY DateCreated DESC
"@

Write-Host ""
Write-Host "Recent Invoicer2Log entries (Details analysis):" -ForegroundColor Yellow

try {
    $detailsResults = sqlcmd -S $serverName -d $databaseName -U $username -P $password -Q $detailsAnalysisQuery -h -1 -s "|"
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "OrderId|Success|Details_Sample|Details_Length|DateCreated" -ForegroundColor Cyan
        Write-Host "-------|-------|--------------|--------------|----------" -ForegroundColor Cyan
        
        foreach ($line in $detailsResults) {
            if ($line -and $line.Trim() -ne "") {
                Write-Host $line
            }
        }
    }
} catch {
    Write-Host "Error in details analysis: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== QUERY COMPLETE ===" -ForegroundColor Green
