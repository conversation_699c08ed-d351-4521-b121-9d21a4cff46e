2025-06-07 15:17:41.211 +10:00 [INF] Starting LEP Invoicer application
2025-06-07 15:17:44.613 +10:00 [INF] Starting LEP Invoicer at "2025-06-07T15:17:44.6009426+10:00"
2025-06-07 15:17:44.650 +10:00 [INF] Initializing services...
2025-06-07 15:17:44.654 +10:00 [INF] Initializing database connection and NHibernate session...
2025-06-07 15:17:44.661 +10:00 [INF] Database connection established successfully
2025-06-07 15:17:44.711 +10:00 [INF] Initializing MYOB service...
2025-06-07 15:17:44.984 +10:00 [ERR] Failed to initialize MYOB service
MYOB.AccountRight.SDK.ApiCommunicationException: Encountered a communication error (https://api.myob.com/accountright)
 ---> System.Net.WebException: The remote server returned an error: (401) Unauthorized.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiGetRequestSync[T](Uri uri, ICompanyFileCredentials credentials, String eTag)
   at MYOB.AccountRight.SDK.Services.CompanyFileService.GetRange()
   at LepInvoicer.Services.MYOBService.<>c__DisplayClass15_0.<InitializeAsync>b__1() in C:\LepSF\au\LepInvoicer\Services\MYOBService.cs:line 70
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Services.MYOBService.InitializeAsync() in C:\LepSF\au\LepInvoicer\Services\MYOBService.cs:line 70
2025-06-07 15:17:45.028 +10:00 [ERR] LEP Invoicer failed after 427ms
MYOB.AccountRight.SDK.ApiCommunicationException: Encountered a communication error (https://api.myob.com/accountright)
 ---> System.Net.WebException: The remote server returned an error: (401) Unauthorized.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiGetRequestSync[T](Uri uri, ICompanyFileCredentials credentials, String eTag)
   at MYOB.AccountRight.SDK.Services.CompanyFileService.GetRange()
   at LepInvoicer.Services.MYOBService.<>c__DisplayClass15_0.<InitializeAsync>b__1() in C:\LepSF\au\LepInvoicer\Services\MYOBService.cs:line 70
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Services.MYOBService.InitializeAsync() in C:\LepSF\au\LepInvoicer\Services\MYOBService.cs:line 70
   at LepInvoicer.Services.InvoicerService.InitializeServicesAsync() in C:\LepSF\au\LepInvoicer\Services\InvoicerService.cs:line 82
   at LepInvoicer.Services.InvoicerService.RunInvoicerAsync() in C:\LepSF\au\LepInvoicer\Services\InvoicerService.cs:line 45
2025-06-07 15:17:45.064 +10:00 [INF] Database connection disposed
2025-06-07 15:17:45.076 +10:00 [INF] LEP Invoicer completed with result: 1
2025-06-07 15:18:41.236 +10:00 [INF] Starting LEP Invoicer application
2025-06-07 15:18:44.062 +10:00 [INF] Starting LEP Invoicer at "2025-06-07T15:18:44.0503842+10:00"
2025-06-07 15:18:44.082 +10:00 [INF] About to initialize services...
2025-06-07 15:18:44.093 +10:00 [INF] Initializing services...
2025-06-07 15:18:44.095 +10:00 [INF] Initializing database service...
2025-06-07 15:18:44.099 +10:00 [INF] Initializing database connection and NHibernate session...
2025-06-07 15:18:44.107 +10:00 [INF] Database connection established successfully
2025-06-07 15:18:44.111 +10:00 [INF] Database service initialized successfully
2025-06-07 15:18:44.114 +10:00 [INF] Initializing MYOB service...
2025-06-07 15:18:44.120 +10:00 [INF] Initializing MYOB service...
2025-06-07 15:18:44.356 +10:00 [ERR] Failed to initialize MYOB service
MYOB.AccountRight.SDK.ApiCommunicationException: Encountered a communication error (https://api.myob.com/accountright)
 ---> System.Net.WebException: The remote server returned an error: (401) Unauthorized.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiGetRequestSync[T](Uri uri, ICompanyFileCredentials credentials, String eTag)
   at MYOB.AccountRight.SDK.Services.CompanyFileService.GetRange()
   at LepInvoicer.Services.MYOBService.<>c__DisplayClass15_0.<InitializeAsync>b__1() in C:\LepSF\au\LepInvoicer\Services\MYOBService.cs:line 70
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Services.MYOBService.InitializeAsync() in C:\LepSF\au\LepInvoicer\Services\MYOBService.cs:line 70
2025-06-07 15:18:44.402 +10:00 [ERR] LEP Invoicer failed after 351ms
MYOB.AccountRight.SDK.ApiCommunicationException: Encountered a communication error (https://api.myob.com/accountright)
 ---> System.Net.WebException: The remote server returned an error: (401) Unauthorized.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiGetRequestSync[T](Uri uri, ICompanyFileCredentials credentials, String eTag)
   at MYOB.AccountRight.SDK.Services.CompanyFileService.GetRange()
   at LepInvoicer.Services.MYOBService.<>c__DisplayClass15_0.<InitializeAsync>b__1() in C:\LepSF\au\LepInvoicer\Services\MYOBService.cs:line 70
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Services.MYOBService.InitializeAsync() in C:\LepSF\au\LepInvoicer\Services\MYOBService.cs:line 70
   at LepInvoicer.Services.InvoicerService.InitializeServicesAsync() in C:\LepSF\au\LepInvoicer\Services\InvoicerService.cs:line 91
   at LepInvoicer.Services.InvoicerService.RunInvoicerAsync() in C:\LepSF\au\LepInvoicer\Services\InvoicerService.cs:line 49
2025-06-07 15:18:44.429 +10:00 [INF] Database connection disposed
2025-06-07 15:18:44.434 +10:00 [INF] LEP Invoicer completed with result: 1
2025-06-07 15:19:57.166 +10:00 [INF] Starting LEP Invoicer application
2025-06-07 15:19:59.813 +10:00 [INF] Starting LEP Invoicer at "2025-06-07T15:19:59.8014997+10:00"
2025-06-07 15:19:59.823 +10:00 [INF] About to initialize services...
2025-06-07 15:19:59.828 +10:00 [INF] Initializing services...
2025-06-07 15:19:59.829 +10:00 [INF] Initializing database service...
2025-06-07 15:19:59.836 +10:00 [INF] Initializing database connection and NHibernate session...
2025-06-07 15:19:59.843 +10:00 [INF] Database connection established successfully
2025-06-07 15:19:59.846 +10:00 [INF] Database service initialized successfully
2025-06-07 15:19:59.847 +10:00 [INF] Initializing MYOB service...
2025-06-07 15:19:59.854 +10:00 [INF] Initializing MYOB service...
2025-06-07 15:19:59.859 +10:00 [INF] Using username/password authentication
2025-06-07 15:20:00.113 +10:00 [ERR] Failed to initialize MYOB service
MYOB.AccountRight.SDK.ApiCommunicationException: Encountered a communication error (https://api.myob.com/accountright)
 ---> System.Net.WebException: The remote server returned an error: (401) Unauthorized.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiGetRequestSync[T](Uri uri, ICompanyFileCredentials credentials, String eTag)
   at MYOB.AccountRight.SDK.Services.CompanyFileService.GetRange()
   at LepInvoicer.Services.MYOBService.<>c__DisplayClass15_0.<InitializeAsync>b__1() in C:\LepSF\au\LepInvoicer\Services\MYOBService.cs:line 76
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Services.MYOBService.InitializeAsync() in C:\LepSF\au\LepInvoicer\Services\MYOBService.cs:line 76
2025-06-07 15:20:00.156 +10:00 [ERR] LEP Invoicer failed after 354ms
MYOB.AccountRight.SDK.ApiCommunicationException: Encountered a communication error (https://api.myob.com/accountright)
 ---> System.Net.WebException: The remote server returned an error: (401) Unauthorized.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiGetRequestSync[T](Uri uri, ICompanyFileCredentials credentials, String eTag)
   at MYOB.AccountRight.SDK.Services.CompanyFileService.GetRange()
   at LepInvoicer.Services.MYOBService.<>c__DisplayClass15_0.<InitializeAsync>b__1() in C:\LepSF\au\LepInvoicer\Services\MYOBService.cs:line 76
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Services.MYOBService.InitializeAsync() in C:\LepSF\au\LepInvoicer\Services\MYOBService.cs:line 76
   at LepInvoicer.Services.InvoicerService.InitializeServicesAsync() in C:\LepSF\au\LepInvoicer\Services\InvoicerService.cs:line 91
   at LepInvoicer.Services.InvoicerService.RunInvoicerAsync() in C:\LepSF\au\LepInvoicer\Services\InvoicerService.cs:line 49
2025-06-07 15:20:00.178 +10:00 [INF] Database connection disposed
2025-06-07 15:20:00.181 +10:00 [INF] LEP Invoicer completed with result: 1
2025-06-07 15:21:49.461 +10:00 [INF] Starting LEP Invoicer application
2025-06-07 15:21:52.210 +10:00 [INF] Starting LEP Invoicer at "2025-06-07T15:21:52.1987476+10:00"
2025-06-07 15:21:52.221 +10:00 [INF] About to initialize services...
2025-06-07 15:21:52.228 +10:00 [INF] Initializing services...
2025-06-07 15:21:52.231 +10:00 [INF] Initializing database service...
2025-06-07 15:21:52.237 +10:00 [INF] Initializing database connection and NHibernate session...
2025-06-07 15:21:52.246 +10:00 [INF] Database connection established successfully
2025-06-07 15:21:52.249 +10:00 [INF] Database service initialized successfully
2025-06-07 15:21:52.250 +10:00 [INF] Initializing MYOB service...
2025-06-07 15:21:52.258 +10:00 [INF] Initializing MYOB service...
2025-06-07 15:21:52.259 +10:00 [WRN] MYOB service running in TEST MODE - MYOB integration disabled
2025-06-07 15:21:52.261 +10:00 [INF] MYOB service initialized successfully
2025-06-07 15:21:52.262 +10:00 [INF] Setting up FastReport...
2025-06-07 15:21:53.376 +10:00 [INF] FastReport setup completed
2025-06-07 15:21:53.378 +10:00 [INF] All services initialized successfully
2025-06-07 15:21:53.382 +10:00 [INF] Services initialization completed
2025-06-07 15:21:53.389 +10:00 [INF] Processing order invoices...
2025-06-07 15:21:53.397 +10:00 [INF] Getting 20 orders to invoice
2025-06-07 15:21:54.784 +10:00 [INF] Found 0 orders to invoice
2025-06-07 15:21:54.787 +10:00 [INF] Found 0 orders to process
2025-06-07 15:21:54.791 +10:00 [INF] Order processing completed. Processed: 0, Success: 0, Failed: 0
2025-06-07 15:21:54.797 +10:00 [INF] Processing credit invoices...
2025-06-07 15:21:54.799 +10:00 [INF] Getting 10 credits to invoice
2025-06-07 15:21:55.058 +10:00 [INF] Found 9 credits to invoice
2025-06-07 15:21:55.063 +10:00 [INF] TEST MODE: Simulating MYOB credit invoice creation for credit 1800
2025-06-07 15:21:55.071 +10:00 [INF] TEST MODE: Simulating MYOB credit invoice creation for credit 1825
2025-06-07 15:21:55.078 +10:00 [INF] TEST MODE: Simulating MYOB credit invoice creation for credit 1826
2025-06-07 15:21:55.085 +10:00 [INF] TEST MODE: Simulating MYOB credit invoice creation for credit 1827
2025-06-07 15:21:55.090 +10:00 [INF] TEST MODE: Simulating MYOB credit invoice creation for credit 1828
2025-06-07 15:21:55.096 +10:00 [INF] TEST MODE: Simulating MYOB credit invoice creation for credit 1829
2025-06-07 15:21:55.100 +10:00 [INF] TEST MODE: Simulating MYOB credit invoice creation for credit 1830
2025-06-07 15:21:55.106 +10:00 [INF] TEST MODE: Simulating MYOB credit invoice creation for credit 1831
2025-06-07 15:21:55.109 +10:00 [INF] TEST MODE: Simulating MYOB credit invoice creation for credit 1832
2025-06-07 15:21:55.115 +10:00 [INF] Processing refund invoices...
2025-06-07 15:21:55.120 +10:00 [INF] Getting 10 refunds to invoice
2025-06-07 15:21:55.138 +10:00 [INF] Found 0 refunds to invoice
2025-06-07 15:21:55.141 +10:00 [INF] Cleaning up invoicer logs...
2025-06-07 15:21:55.217 +10:00 [INF] LEP Invoicer completed successfully in 3018ms. Orders: 0, Credits: 9, Refunds: 0
2025-06-07 15:21:55.228 +10:00 [INF] Database connection disposed
2025-06-07 15:21:55.230 +10:00 [INF] LEP Invoicer completed with result: 0
2025-06-07 15:26:46.624 +10:00 [INF] Starting LEP Invoicer application
2025-06-07 15:26:49.179 +10:00 [INF] Starting LEP Invoicer at "2025-06-07T15:26:49.1678500+10:00"
2025-06-07 15:26:49.191 +10:00 [INF] About to initialize services...
2025-06-07 15:26:49.195 +10:00 [INF] Initializing services...
2025-06-07 15:26:49.197 +10:00 [INF] Initializing database service...
2025-06-07 15:26:49.199 +10:00 [INF] Initializing database connection and NHibernate session...
2025-06-07 15:26:49.211 +10:00 [INF] Database connection established successfully
2025-06-07 15:26:49.212 +10:00 [INF] Database service initialized successfully
2025-06-07 15:26:49.214 +10:00 [INF] Initializing MYOB service...
2025-06-07 15:26:49.224 +10:00 [INF] Initializing MYOB service...
2025-06-07 15:26:49.228 +10:00 [INF] MYOB Configuration Debug:
2025-06-07 15:26:49.231 +10:00 [INF]   DeveloperKey: [SET - Length: 36]
2025-06-07 15:26:49.237 +10:00 [INF]   DeveloperSecret: [SET - Length: 24]
2025-06-07 15:26:49.250 +10:00 [INF]   ConfirmationUrl: 'http://desktop'
2025-06-07 15:26:49.253 +10:00 [INF]   CompanyFileName: 'LEP Colour Printers Pty Ltd'
2025-06-07 15:26:49.258 +10:00 [INF]   Username: [SET]
2025-06-07 15:26:49.261 +10:00 [INF]   Password: [SET]
2025-06-07 15:26:49.266 +10:00 [INF] Using username/password authentication
2025-06-07 15:26:49.505 +10:00 [ERR] Failed to initialize MYOB service
MYOB.AccountRight.SDK.ApiCommunicationException: Encountered a communication error (https://api.myob.com/accountright)
 ---> System.Net.WebException: The remote server returned an error: (401) Unauthorized.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiGetRequestSync[T](Uri uri, ICompanyFileCredentials credentials, String eTag)
   at MYOB.AccountRight.SDK.Services.CompanyFileService.GetRange()
   at LepInvoicer.Services.MYOBService.<>c__DisplayClass15_0.<InitializeAsync>b__1() in C:\LepSF\au\LepInvoicer\Services\MYOBService.cs:line 85
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Services.MYOBService.InitializeAsync() in C:\LepSF\au\LepInvoicer\Services\MYOBService.cs:line 85
2025-06-07 15:26:49.544 +10:00 [ERR] LEP Invoicer failed after 376ms
MYOB.AccountRight.SDK.ApiCommunicationException: Encountered a communication error (https://api.myob.com/accountright)
 ---> System.Net.WebException: The remote server returned an error: (401) Unauthorized.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiGetRequestSync[T](Uri uri, ICompanyFileCredentials credentials, String eTag)
   at MYOB.AccountRight.SDK.Services.CompanyFileService.GetRange()
   at LepInvoicer.Services.MYOBService.<>c__DisplayClass15_0.<InitializeAsync>b__1() in C:\LepSF\au\LepInvoicer\Services\MYOBService.cs:line 85
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Services.MYOBService.InitializeAsync() in C:\LepSF\au\LepInvoicer\Services\MYOBService.cs:line 85
   at LepInvoicer.Services.InvoicerService.InitializeServicesAsync() in C:\LepSF\au\LepInvoicer\Services\InvoicerService.cs:line 91
   at LepInvoicer.Services.InvoicerService.RunInvoicerAsync() in C:\LepSF\au\LepInvoicer\Services\InvoicerService.cs:line 49
2025-06-07 15:26:49.573 +10:00 [INF] Database connection disposed
2025-06-07 15:26:49.578 +10:00 [INF] LEP Invoicer completed with result: 1
2025-06-07 15:27:19.697 +10:00 [INF] Starting LEP Invoicer application
2025-06-07 15:27:20.455 +10:00 [INF] MYOBService constructor - Configuration loaded. TestMode: false
2025-06-07 15:27:20.464 +10:00 [INF] MYOBService constructor - MYOB DeveloperKey: [SET]
2025-06-07 15:27:22.319 +10:00 [INF] Starting LEP Invoicer at "2025-06-07T15:27:22.3192651+10:00"
2025-06-07 15:27:22.324 +10:00 [INF] About to initialize services...
2025-06-07 15:27:22.329 +10:00 [INF] Initializing services...
2025-06-07 15:27:22.330 +10:00 [INF] Initializing database service...
2025-06-07 15:27:22.332 +10:00 [INF] Initializing database connection and NHibernate session...
2025-06-07 15:27:22.338 +10:00 [INF] Database connection established successfully
2025-06-07 15:27:22.339 +10:00 [INF] Database service initialized successfully
2025-06-07 15:27:22.340 +10:00 [INF] Initializing MYOB service...
2025-06-07 15:27:22.349 +10:00 [INF] Initializing MYOB service...
2025-06-07 15:27:22.350 +10:00 [INF] MYOB Configuration Debug:
2025-06-07 15:27:22.354 +10:00 [INF]   DeveloperKey: [SET - Length: 36]
2025-06-07 15:27:22.358 +10:00 [INF]   DeveloperSecret: [SET - Length: 24]
2025-06-07 15:27:22.362 +10:00 [INF]   ConfirmationUrl: 'http://desktop'
2025-06-07 15:27:22.370 +10:00 [INF]   CompanyFileName: 'LEP Colour Printers Pty Ltd'
2025-06-07 15:27:22.376 +10:00 [INF]   Username: [SET]
2025-06-07 15:27:22.380 +10:00 [INF]   Password: [SET]
2025-06-07 15:27:22.396 +10:00 [INF] Using username/password authentication
2025-06-07 15:27:22.619 +10:00 [ERR] Failed to initialize MYOB service
MYOB.AccountRight.SDK.ApiCommunicationException: Encountered a communication error (https://api.myob.com/accountright)
 ---> System.Net.WebException: The remote server returned an error: (401) Unauthorized.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiGetRequestSync[T](Uri uri, ICompanyFileCredentials credentials, String eTag)
   at MYOB.AccountRight.SDK.Services.CompanyFileService.GetRange()
   at LepInvoicer.Services.MYOBService.<>c__DisplayClass15_0.<InitializeAsync>b__1() in C:\LepSF\au\LepInvoicer\Services\MYOBService.cs:line 90
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Services.MYOBService.InitializeAsync() in C:\LepSF\au\LepInvoicer\Services\MYOBService.cs:line 90
2025-06-07 15:27:22.652 +10:00 [ERR] LEP Invoicer failed after 332ms
MYOB.AccountRight.SDK.ApiCommunicationException: Encountered a communication error (https://api.myob.com/accountright)
 ---> System.Net.WebException: The remote server returned an error: (401) Unauthorized.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiGetRequestSync[T](Uri uri, ICompanyFileCredentials credentials, String eTag)
   at MYOB.AccountRight.SDK.Services.CompanyFileService.GetRange()
   at LepInvoicer.Services.MYOBService.<>c__DisplayClass15_0.<InitializeAsync>b__1() in C:\LepSF\au\LepInvoicer\Services\MYOBService.cs:line 90
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Services.MYOBService.InitializeAsync() in C:\LepSF\au\LepInvoicer\Services\MYOBService.cs:line 90
   at LepInvoicer.Services.InvoicerService.InitializeServicesAsync() in C:\LepSF\au\LepInvoicer\Services\InvoicerService.cs:line 91
   at LepInvoicer.Services.InvoicerService.RunInvoicerAsync() in C:\LepSF\au\LepInvoicer\Services\InvoicerService.cs:line 49
2025-06-07 15:27:22.681 +10:00 [INF] Database connection disposed
2025-06-07 15:27:22.685 +10:00 [INF] LEP Invoicer completed with result: 1
2025-06-07 15:27:44.234 +10:00 [INF] Starting LEP Invoicer application
2025-06-07 15:27:44.325 +10:00 [INF] Creating host builder...
2025-06-07 15:27:44.333 +10:00 [INF] Building host...
2025-06-07 15:27:45.028 +10:00 [INF] Getting invoicer service...
2025-06-07 15:27:45.162 +10:00 [INF] MYOBService constructor - Configuration loaded. TestMode: false
2025-06-07 15:27:45.173 +10:00 [INF] MYOBService constructor - MYOB DeveloperKey: [SET]
2025-06-07 15:27:47.148 +10:00 [INF] Running invoicer...
2025-06-07 15:27:47.151 +10:00 [INF] Starting LEP Invoicer at "2025-06-07T15:27:47.1514663+10:00"
2025-06-07 15:27:47.156 +10:00 [INF] About to initialize services...
2025-06-07 15:27:47.162 +10:00 [INF] Initializing services...
2025-06-07 15:27:47.163 +10:00 [INF] Initializing database service...
2025-06-07 15:27:47.165 +10:00 [INF] Initializing database connection and NHibernate session...
2025-06-07 15:27:47.173 +10:00 [INF] Database connection established successfully
2025-06-07 15:27:47.176 +10:00 [INF] Database service initialized successfully
2025-06-07 15:27:47.177 +10:00 [INF] Initializing MYOB service...
2025-06-07 15:27:47.187 +10:00 [INF] Initializing MYOB service...
2025-06-07 15:27:47.190 +10:00 [INF] MYOB Configuration Debug:
2025-06-07 15:27:47.193 +10:00 [INF]   DeveloperKey: [SET - Length: 36]
2025-06-07 15:27:47.202 +10:00 [INF]   DeveloperSecret: [SET - Length: 24]
2025-06-07 15:27:47.205 +10:00 [INF]   ConfirmationUrl: 'http://desktop'
2025-06-07 15:27:47.206 +10:00 [INF]   CompanyFileName: 'LEP Colour Printers Pty Ltd'
2025-06-07 15:27:47.209 +10:00 [INF]   Username: [SET]
2025-06-07 15:27:47.211 +10:00 [INF]   Password: [SET]
2025-06-07 15:27:47.216 +10:00 [INF] Using username/password authentication
2025-06-07 15:27:47.432 +10:00 [ERR] Failed to initialize MYOB service
MYOB.AccountRight.SDK.ApiCommunicationException: Encountered a communication error (https://api.myob.com/accountright)
 ---> System.Net.WebException: The remote server returned an error: (401) Unauthorized.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiGetRequestSync[T](Uri uri, ICompanyFileCredentials credentials, String eTag)
   at MYOB.AccountRight.SDK.Services.CompanyFileService.GetRange()
   at LepInvoicer.Services.MYOBService.<>c__DisplayClass15_0.<InitializeAsync>b__1() in C:\LepSF\au\LepInvoicer\Services\MYOBService.cs:line 90
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Services.MYOBService.InitializeAsync() in C:\LepSF\au\LepInvoicer\Services\MYOBService.cs:line 90
2025-06-07 15:27:47.468 +10:00 [ERR] LEP Invoicer failed after 316ms
MYOB.AccountRight.SDK.ApiCommunicationException: Encountered a communication error (https://api.myob.com/accountright)
 ---> System.Net.WebException: The remote server returned an error: (401) Unauthorized.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiGetRequestSync[T](Uri uri, ICompanyFileCredentials credentials, String eTag)
   at MYOB.AccountRight.SDK.Services.CompanyFileService.GetRange()
   at LepInvoicer.Services.MYOBService.<>c__DisplayClass15_0.<InitializeAsync>b__1() in C:\LepSF\au\LepInvoicer\Services\MYOBService.cs:line 90
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Services.MYOBService.InitializeAsync() in C:\LepSF\au\LepInvoicer\Services\MYOBService.cs:line 90
   at LepInvoicer.Services.InvoicerService.InitializeServicesAsync() in C:\LepSF\au\LepInvoicer\Services\InvoicerService.cs:line 91
   at LepInvoicer.Services.InvoicerService.RunInvoicerAsync() in C:\LepSF\au\LepInvoicer\Services\InvoicerService.cs:line 49
2025-06-07 15:27:47.503 +10:00 [INF] Database connection disposed
2025-06-07 15:27:47.506 +10:00 [INF] LEP Invoicer completed with result: 1
2025-06-07 15:31:40.799 +10:00 [INF] Starting LEP Invoicer application
2025-06-07 15:31:40.896 +10:00 [INF] Creating host builder...
2025-06-07 15:31:40.905 +10:00 [INF] Building host...
2025-06-07 15:31:41.540 +10:00 [INF] Getting invoicer service...
2025-06-07 15:31:41.666 +10:00 [INF] MYOBService constructor - Configuration loaded. TestMode: false
2025-06-07 15:31:41.675 +10:00 [INF] MYOBService constructor - MYOB DeveloperKey: [SET]
2025-06-07 15:31:43.650 +10:00 [INF] Running invoicer...
2025-06-07 15:31:43.659 +10:00 [INF] Starting LEP Invoicer at "2025-06-07T15:31:43.6586829+10:00"
2025-06-07 15:31:43.670 +10:00 [INF] About to initialize services...
2025-06-07 15:31:43.677 +10:00 [INF] Initializing services...
2025-06-07 15:31:43.680 +10:00 [INF] Initializing database service...
2025-06-07 15:31:43.686 +10:00 [INF] Initializing database connection and NHibernate session...
2025-06-07 15:31:43.693 +10:00 [INF] Database connection established successfully
2025-06-07 15:31:43.695 +10:00 [INF] Database service initialized successfully
2025-06-07 15:31:43.706 +10:00 [INF] Initializing MYOB service...
2025-06-07 15:31:43.721 +10:00 [INF] Initializing MYOB service...
2025-06-07 15:31:43.724 +10:00 [INF] MYOB Configuration Debug:
2025-06-07 15:31:43.737 +10:00 [INF]   DeveloperKey: [SET - Length: 36]
2025-06-07 15:31:43.744 +10:00 [INF]   DeveloperSecret: [SET - Length: 24]
2025-06-07 15:31:43.746 +10:00 [INF]   ConfirmationUrl: 'http://desktop'
2025-06-07 15:31:43.753 +10:00 [INF]   CompanyFileName: 'LEP Colour Printers Pty Ltd'
2025-06-07 15:31:43.756 +10:00 [INF]   Username: [SET]
2025-06-07 15:31:43.762 +10:00 [INF]   Password: [SET]
2025-06-07 15:31:43.766 +10:00 [INF] Initializing MYOB with OAuth authentication
2025-06-07 15:31:43.771 +10:00 [INF] Starting OAuth authentication flow
2025-06-07 15:31:43.924 +10:00 [INF] Using existing OAuth tokens
2025-06-07 15:31:43.938 +10:00 [INF] Getting company files from MYOB
2025-06-07 15:31:44.282 +10:00 [INF] Found 1 company files
2025-06-07 15:31:44.285 +10:00 [INF] MYOB service initialized with OAuth credentials. Using company file: LEP Colour Printers Pty Ltd
2025-06-07 15:31:45.121 +10:00 [INF] Cached 279 accounts from MYOB
2025-06-07 15:31:45.462 +10:00 [INF] Cached GST tax code from MYOB
2025-06-07 15:31:45.614 +10:00 [INF] MYOB service initialized successfully with company file: LEP Colour Printers Pty Ltd
2025-06-07 15:31:45.617 +10:00 [INF] MYOB service initialized successfully
2025-06-07 15:31:45.619 +10:00 [INF] Setting up FastReport...
2025-06-07 15:31:45.644 +10:00 [INF] FastReport setup completed
2025-06-07 15:31:45.664 +10:00 [INF] All services initialized successfully
2025-06-07 15:31:45.666 +10:00 [INF] Services initialization completed
2025-06-07 15:31:45.672 +10:00 [INF] Processing order invoices...
2025-06-07 15:31:45.676 +10:00 [INF] Getting 20 orders to invoice
2025-06-07 15:31:46.592 +10:00 [INF] Found 0 orders to invoice
2025-06-07 15:31:46.594 +10:00 [INF] Found 0 orders to process
2025-06-07 15:31:46.597 +10:00 [INF] Order processing completed. Processed: 0, Success: 0, Failed: 0
2025-06-07 15:31:46.600 +10:00 [INF] Processing credit invoices...
2025-06-07 15:31:46.603 +10:00 [INF] Getting 10 credits to invoice
2025-06-07 15:31:46.625 +10:00 [INF] Found 0 credits to invoice
2025-06-07 15:31:46.632 +10:00 [INF] Processing refund invoices...
2025-06-07 15:31:46.643 +10:00 [INF] Getting 10 refunds to invoice
2025-06-07 15:31:46.651 +10:00 [INF] Found 0 refunds to invoice
2025-06-07 15:31:46.653 +10:00 [INF] Cleaning up invoicer logs...
2025-06-07 15:31:46.727 +10:00 [INF] LEP Invoicer completed successfully in 3068ms. Orders: 0, Credits: 0, Refunds: 0
2025-06-07 15:31:46.749 +10:00 [INF] Database connection disposed
2025-06-07 15:31:46.752 +10:00 [INF] LEP Invoicer completed with result: 0
2025-06-07 15:34:46.889 +10:00 [INF] Starting LEP Invoicer application
2025-06-07 15:34:49.567 +10:00 [INF] Starting LEP Invoicer at "2025-06-07T15:34:49.5572533+10:00"
2025-06-07 15:34:49.581 +10:00 [INF] Initializing services...
2025-06-07 15:34:49.584 +10:00 [INF] Initializing database connection and NHibernate session...
2025-06-07 15:34:49.591 +10:00 [INF] Database connection established successfully
2025-06-07 15:34:49.599 +10:00 [INF] Initializing MYOB service...
2025-06-07 15:34:49.604 +10:00 [INF] Initializing MYOB with OAuth authentication
2025-06-07 15:34:49.608 +10:00 [INF] Starting OAuth authentication flow
2025-06-07 15:34:49.727 +10:00 [INF] Using existing OAuth tokens
2025-06-07 15:34:49.735 +10:00 [INF] Getting company files from MYOB
2025-06-07 15:34:50.057 +10:00 [INF] MYOB service initialized with OAuth credentials. Using company file: LEP Colour Printers Pty Ltd
2025-06-07 15:34:50.690 +10:00 [INF] Cached 279 accounts from MYOB
2025-06-07 15:34:51.081 +10:00 [INF] Cached GST tax code from MYOB
2025-06-07 15:34:51.234 +10:00 [INF] MYOB service initialized successfully with company file: LEP Colour Printers Pty Ltd
2025-06-07 15:34:51.252 +10:00 [INF] Services initialized successfully
2025-06-07 15:34:51.259 +10:00 [INF] Processing order invoices...
2025-06-07 15:34:51.265 +10:00 [INF] Getting 20 orders to invoice
2025-06-07 15:34:52.190 +10:00 [INF] Found 0 orders to invoice
2025-06-07 15:34:52.193 +10:00 [INF] Found 0 orders to process
2025-06-07 15:34:52.195 +10:00 [INF] Order processing completed. Processed: 0, Success: 0, Failed: 0
2025-06-07 15:34:52.204 +10:00 [INF] Processing credit invoices...
2025-06-07 15:34:52.206 +10:00 [INF] Getting 10 credits to invoice
2025-06-07 15:34:52.232 +10:00 [INF] Found 0 credits to invoice
2025-06-07 15:34:52.237 +10:00 [INF] Processing refund invoices...
2025-06-07 15:34:52.241 +10:00 [INF] Getting 10 refunds to invoice
2025-06-07 15:34:52.253 +10:00 [INF] Found 0 refunds to invoice
2025-06-07 15:34:52.260 +10:00 [INF] Cleaning up invoicer logs...
2025-06-07 15:34:52.335 +10:00 [INF] LEP Invoicer completed successfully in 2777ms. Orders: 0, Credits: 0, Refunds: 0
2025-06-07 15:34:52.341 +10:00 [INF] Database connection disposed
2025-06-07 15:34:52.344 +10:00 [INF] LEP Invoicer completed with result: 0
2025-06-07 15:35:12.191 +10:00 [INF] Starting LEP Invoicer application
2025-06-07 15:35:14.966 +10:00 [INF] Starting LEP Invoicer at "2025-06-07T15:35:14.9552508+10:00"
2025-06-07 15:35:14.981 +10:00 [INF] Initializing services...
2025-06-07 15:35:14.984 +10:00 [INF] Initializing database connection and NHibernate session...
2025-06-07 15:35:14.990 +10:00 [INF] Database connection established successfully
2025-06-07 15:35:14.996 +10:00 [INF] Initializing MYOB service...
2025-06-07 15:35:15.000 +10:00 [INF] Initializing MYOB with OAuth authentication
2025-06-07 15:35:15.004 +10:00 [INF] Starting OAuth authentication flow
2025-06-07 15:35:15.117 +10:00 [INF] Using existing OAuth tokens
2025-06-07 15:35:15.125 +10:00 [INF] Getting company files from MYOB
2025-06-07 15:35:15.489 +10:00 [INF] MYOB service initialized with OAuth credentials. Using company file: LEP Colour Printers Pty Ltd
2025-06-07 15:35:16.254 +10:00 [INF] Cached 279 accounts from MYOB
2025-06-07 15:35:16.907 +10:00 [INF] Cached GST tax code from MYOB
2025-06-07 15:35:17.065 +10:00 [INF] MYOB service initialized successfully with company file: LEP Colour Printers Pty Ltd
2025-06-07 15:35:17.077 +10:00 [INF] Services initialized successfully
2025-06-07 15:35:17.085 +10:00 [INF] Processing order invoices...
2025-06-07 15:35:17.092 +10:00 [INF] Getting 20 orders to invoice
2025-06-07 15:35:18.060 +10:00 [INF] Found 0 orders to invoice
2025-06-07 15:35:18.062 +10:00 [INF] Found 0 orders to process
2025-06-07 15:35:18.064 +10:00 [INF] Order processing completed. Processed: 0, Success: 0, Failed: 0
2025-06-07 15:35:18.068 +10:00 [INF] Processing credit invoices...
2025-06-07 15:35:18.070 +10:00 [INF] Getting 10 credits to invoice
2025-06-07 15:35:18.096 +10:00 [INF] Found 0 credits to invoice
2025-06-07 15:35:18.101 +10:00 [INF] Processing refund invoices...
2025-06-07 15:35:18.107 +10:00 [INF] Getting 10 refunds to invoice
2025-06-07 15:35:18.118 +10:00 [INF] Found 0 refunds to invoice
2025-06-07 15:35:18.122 +10:00 [INF] Cleaning up invoicer logs...
2025-06-07 15:35:18.195 +10:00 [INF] LEP Invoicer completed successfully in 3240ms. Orders: 0, Credits: 0, Refunds: 0
2025-06-07 15:35:18.201 +10:00 [INF] Database connection disposed
2025-06-07 15:35:18.203 +10:00 [INF] LEP Invoicer completed with result: 0
2025-06-07 15:36:06.650 +10:00 [INF] Starting LEP Invoicer application
2025-06-07 15:36:09.404 +10:00 [INF] Starting LEP Invoicer at "2025-06-07T15:36:09.3917630+10:00"
2025-06-07 15:36:09.418 +10:00 [INF] Initializing services...
2025-06-07 15:36:09.422 +10:00 [INF] Initializing database connection and NHibernate session...
2025-06-07 15:36:09.435 +10:00 [INF] Database connection established successfully
2025-06-07 15:36:09.441 +10:00 [INF] Initializing MYOB service...
2025-06-07 15:36:09.445 +10:00 [WRN] MYOB service running in TEST MODE - MYOB integration disabled
2025-06-07 15:36:09.459 +10:00 [INF] Services initialized successfully
2025-06-07 15:36:09.475 +10:00 [INF] Processing order invoices...
2025-06-07 15:36:09.481 +10:00 [INF] Getting 20 orders to invoice
2025-06-07 15:36:10.399 +10:00 [INF] Found 0 orders to invoice
2025-06-07 15:36:10.411 +10:00 [INF] Found 0 orders to process
2025-06-07 15:36:10.417 +10:00 [INF] Order processing completed. Processed: 0, Success: 0, Failed: 0
2025-06-07 15:36:10.425 +10:00 [INF] Processing credit invoices...
2025-06-07 15:36:10.428 +10:00 [INF] Getting 10 credits to invoice
2025-06-07 15:36:10.457 +10:00 [INF] Found 0 credits to invoice
2025-06-07 15:36:10.462 +10:00 [INF] Processing refund invoices...
2025-06-07 15:36:10.465 +10:00 [INF] Getting 10 refunds to invoice
2025-06-07 15:36:10.475 +10:00 [INF] Found 0 refunds to invoice
2025-06-07 15:36:10.477 +10:00 [INF] Cleaning up invoicer logs...
2025-06-07 15:36:10.556 +10:00 [INF] LEP Invoicer completed successfully in 1164ms. Orders: 0, Credits: 0, Refunds: 0
2025-06-07 15:36:10.563 +10:00 [INF] Database connection disposed
2025-06-07 15:36:10.566 +10:00 [INF] LEP Invoicer completed with result: 0
2025-06-07 15:41:42.911 +10:00 [INF] Starting LEP Invoicer application
2025-06-07 15:41:43.695 +10:00 [INF] MYOBService constructor - Configuration loaded
2025-06-07 15:41:43.708 +10:00 [INF] MYOB Config - DeveloperKey: 'af973795-f697-4bec-be3d-6fd70da8d5dd'
2025-06-07 15:41:43.713 +10:00 [INF] MYOB Config - DeveloperSecret: '8GDEXoCS1MH3aOT4SbXJM32Y'
2025-06-07 15:41:43.715 +10:00 [INF] MYOB Config - ConfirmationUrl: 'http://desktop'
2025-06-07 15:41:43.717 +10:00 [INF] MYOB Config - CompanyFileName: 'LEP Colour Printers Pty Ltd'
2025-06-07 15:41:45.712 +10:00 [INF] Starting LEP Invoicer at "2025-06-07T15:41:45.7117394+10:00"
2025-06-07 15:41:45.723 +10:00 [INF] Initializing services...
2025-06-07 15:41:45.725 +10:00 [INF] Initializing database connection and NHibernate session...
2025-06-07 15:41:45.732 +10:00 [INF] Database connection established successfully
2025-06-07 15:41:45.739 +10:00 [INF] Initializing MYOB service...
2025-06-07 15:41:45.743 +10:00 [INF] Initializing MYOB with OAuth authentication
2025-06-07 15:41:45.749 +10:00 [INF] Starting OAuth authentication flow
2025-06-07 15:41:45.866 +10:00 [INF] Using existing OAuth tokens
2025-06-07 15:41:45.875 +10:00 [INF] Getting company files from MYOB
2025-06-07 15:41:46.418 +10:00 [INF] MYOB service initialized with OAuth credentials. Using company file: LEP Colour Printers Pty Ltd
2025-06-07 15:41:47.206 +10:00 [INF] Cached 279 accounts from MYOB
2025-06-07 15:41:47.542 +10:00 [INF] Cached GST tax code from MYOB
2025-06-07 15:41:47.694 +10:00 [INF] MYOB service initialized successfully with company file: LEP Colour Printers Pty Ltd
2025-06-07 15:41:47.709 +10:00 [INF] Services initialized successfully
2025-06-07 15:41:47.718 +10:00 [INF] Processing order invoices...
2025-06-07 15:41:47.723 +10:00 [INF] Getting 20 orders to invoice
2025-06-07 15:41:48.702 +10:00 [INF] Found 0 orders to invoice
2025-06-07 15:41:48.706 +10:00 [INF] Found 0 orders to process
2025-06-07 15:41:48.710 +10:00 [INF] Order processing completed. Processed: 0, Success: 0, Failed: 0
2025-06-07 15:41:48.715 +10:00 [INF] Processing credit invoices...
2025-06-07 15:41:48.717 +10:00 [INF] Getting 10 credits to invoice
2025-06-07 15:41:48.738 +10:00 [INF] Found 0 credits to invoice
2025-06-07 15:41:48.745 +10:00 [INF] Processing refund invoices...
2025-06-07 15:41:48.748 +10:00 [INF] Getting 10 refunds to invoice
2025-06-07 15:41:48.758 +10:00 [INF] Found 0 refunds to invoice
2025-06-07 15:41:48.762 +10:00 [INF] Cleaning up invoicer logs...
2025-06-07 15:41:48.834 +10:00 [INF] LEP Invoicer completed successfully in 3122ms. Orders: 0, Credits: 0, Refunds: 0
2025-06-07 15:41:48.841 +10:00 [INF] Database connection disposed
2025-06-07 15:41:48.844 +10:00 [INF] LEP Invoicer completed with result: 0
2025-06-07 16:06:48.813 +10:00 [INF] Starting LEP Invoicer application
2025-06-07 16:06:51.655 +10:00 [INF] Starting LEP Invoicer at "2025-06-07T16:06:51.6451020+10:00"
2025-06-07 16:06:51.669 +10:00 [INF] Initializing services...
2025-06-07 16:06:51.672 +10:00 [INF] Initializing database connection and NHibernate session...
2025-06-07 16:06:51.680 +10:00 [INF] Database connection established successfully
2025-06-07 16:06:51.690 +10:00 [INF] Initializing MYOB service...
2025-06-07 16:06:51.703 +10:00 [INF] Initializing MYOB with OAuth authentication
2025-06-07 16:06:51.707 +10:00 [INF] Starting OAuth authentication flow
2025-06-07 16:06:51.828 +10:00 [INF] Using existing OAuth tokens
2025-06-07 16:06:51.833 +10:00 [INF] Getting company files from MYOB
2025-06-07 16:06:52.241 +10:00 [INF] MYOB service initialized with OAuth credentials. Using company file: LEP Colour Printers Pty Ltd
2025-06-07 16:07:54.038 +10:00 [INF] Starting LEP Invoicer application
2025-06-07 16:07:56.983 +10:00 [INF] Starting LEP Invoicer at "2025-06-07T16:07:56.9725352+10:00"
2025-06-07 16:07:56.994 +10:00 [INF] Initializing services...
2025-06-07 16:07:56.997 +10:00 [INF] Initializing database connection and NHibernate session...
2025-06-07 16:07:57.006 +10:00 [INF] Database connection established successfully
2025-06-07 16:07:57.013 +10:00 [INF] Initializing MYOB service...
2025-06-07 16:07:57.016 +10:00 [WRN] MYOB service running in TEST MODE - MYOB integration disabled
2025-06-07 16:07:57.046 +10:00 [INF] Services initialized successfully
2025-06-07 16:07:57.052 +10:00 [INF] Processing order invoices...
2025-06-07 16:07:57.056 +10:00 [INF] Getting 20 orders to invoice
2025-06-07 16:07:57.937 +10:00 [INF] Found 0 orders to invoice
2025-06-07 16:07:57.942 +10:00 [INF] Found 0 orders to process
2025-06-07 16:07:57.945 +10:00 [INF] Order processing completed. Processed: 0, Success: 0, Failed: 0
2025-06-07 16:07:57.951 +10:00 [INF] Processing credit invoices...
2025-06-07 16:07:57.954 +10:00 [INF] Getting 10 credits to invoice
2025-06-07 16:07:57.979 +10:00 [INF] Found 0 credits to invoice
2025-06-07 16:07:57.984 +10:00 [INF] Processing refund invoices...
2025-06-07 16:07:57.986 +10:00 [INF] Getting 10 refunds to invoice
2025-06-07 16:07:57.995 +10:00 [INF] Found 0 refunds to invoice
2025-06-07 16:07:57.998 +10:00 [INF] Cleaning up invoicer logs...
2025-06-07 16:07:58.102 +10:00 [INF] LEP Invoicer completed successfully in 1129ms. Orders: 0, Credits: 0, Refunds: 0
2025-06-07 16:07:58.110 +10:00 [INF] Database connection disposed
2025-06-07 16:07:58.113 +10:00 [INF] LEP Invoicer completed with result: 0
2025-06-07 16:08:31.145 +10:00 [INF] Starting LEP Invoicer application
2025-06-07 16:08:33.986 +10:00 [INF] Starting LEP Invoicer at "2025-06-07T16:08:33.9760343+10:00"
2025-06-07 16:08:33.999 +10:00 [INF] Initializing services...
2025-06-07 16:08:34.001 +10:00 [INF] Initializing database connection and NHibernate session...
2025-06-07 16:08:34.007 +10:00 [INF] Database connection established successfully
2025-06-07 16:08:34.018 +10:00 [INF] Initializing MYOB service...
2025-06-07 16:08:34.022 +10:00 [WRN] MYOB service running in TEST MODE - MYOB integration disabled
2025-06-07 16:08:34.036 +10:00 [INF] Services initialized successfully
2025-06-07 16:08:34.062 +10:00 [INF] Processing order invoices...
2025-06-07 16:08:34.067 +10:00 [INF] Getting 20 orders to invoice
2025-06-07 16:08:34.938 +10:00 [INF] Found 0 orders to invoice
2025-06-07 16:08:34.941 +10:00 [INF] Found 0 orders to process
2025-06-07 16:08:34.943 +10:00 [INF] Order processing completed. Processed: 0, Success: 0, Failed: 0
2025-06-07 16:08:34.947 +10:00 [INF] Processing credit invoices...
2025-06-07 16:08:34.954 +10:00 [INF] Getting 10 credits to invoice
2025-06-07 16:08:34.979 +10:00 [INF] Found 0 credits to invoice
2025-06-07 16:08:35.003 +10:00 [INF] Processing refund invoices...
2025-06-07 16:08:35.005 +10:00 [INF] Getting 10 refunds to invoice
2025-06-07 16:08:35.015 +10:00 [INF] Found 0 refunds to invoice
2025-06-07 16:08:35.017 +10:00 [INF] Cleaning up invoicer logs...
2025-06-07 16:08:35.100 +10:00 [INF] LEP Invoicer completed successfully in 1124ms. Orders: 0, Credits: 0, Refunds: 0
2025-06-07 16:08:35.111 +10:00 [INF] Database connection disposed
2025-06-07 16:08:35.117 +10:00 [INF] LEP Invoicer completed with result: 0
2025-06-07 16:09:30.442 +10:00 [INF] Starting LEP Invoicer application
2025-06-07 16:09:33.584 +10:00 [INF] Starting LEP Invoicer at "2025-06-07T16:09:33.5730960+10:00"
2025-06-07 16:09:33.611 +10:00 [INF] Initializing services...
2025-06-07 16:09:33.614 +10:00 [INF] Initializing database connection and NHibernate session...
2025-06-07 16:09:33.622 +10:00 [INF] Database connection established successfully
2025-06-07 16:09:33.628 +10:00 [INF] Initializing MYOB service...
2025-06-07 16:09:33.631 +10:00 [WRN] MYOB service running in TEST MODE - MYOB integration disabled
2025-06-07 16:09:33.643 +10:00 [INF] Services initialized successfully
2025-06-07 16:09:33.653 +10:00 [INF] Processing order invoices...
2025-06-07 16:09:33.666 +10:00 [INF] Getting 20 orders to invoice
2025-06-07 16:09:34.557 +10:00 [INF] Found 0 orders to invoice
2025-06-07 16:09:34.560 +10:00 [INF] Found 0 orders to process
2025-06-07 16:09:34.562 +10:00 [INF] Order processing completed. Processed: 0, Success: 0, Failed: 0
2025-06-07 16:09:34.567 +10:00 [INF] Processing credit invoices...
2025-06-07 16:09:34.573 +10:00 [INF] Getting 10 credits to invoice
2025-06-07 16:09:34.600 +10:00 [INF] Found 0 credits to invoice
2025-06-07 16:09:34.631 +10:00 [INF] Processing refund invoices...
2025-06-07 16:09:34.639 +10:00 [INF] Getting 10 refunds to invoice
2025-06-07 16:09:34.648 +10:00 [INF] Found 0 refunds to invoice
2025-06-07 16:09:34.651 +10:00 [INF] Cleaning up invoicer logs...
2025-06-07 16:09:34.724 +10:00 [INF] LEP Invoicer completed successfully in 1151ms. Orders: 0, Credits: 0, Refunds: 0
2025-06-07 16:09:34.733 +10:00 [INF] Database connection disposed
2025-06-07 16:09:34.764 +10:00 [INF] LEP Invoicer completed with result: 0
2025-06-07 16:16:27.607 +10:00 [INF] Starting LEP Invoicer application
2025-06-07 16:16:30.319 +10:00 [INF] Starting LEP Invoicer at "2025-06-07T16:16:30.3093195+10:00"
2025-06-07 16:16:30.334 +10:00 [INF] Initializing services...
2025-06-07 16:16:30.338 +10:00 [INF] Initializing database connection and NHibernate session...
2025-06-07 16:16:30.345 +10:00 [INF] Database connection established successfully
2025-06-07 16:16:30.353 +10:00 [INF] Initializing MYOB service...
2025-06-07 16:16:30.355 +10:00 [WRN] MYOB service running in TEST MODE - MYOB integration disabled
2025-06-07 16:16:30.366 +10:00 [INF] Services initialized successfully
2025-06-07 16:16:30.915 +10:00 [INF] OrderCredit Type Summary: 
2025-06-07 16:16:30.920 +10:00 [INF] Processing order invoices...
2025-06-07 16:16:30.926 +10:00 [INF] Getting 20 orders to invoice
2025-06-07 16:16:31.412 +10:00 [INF] Found 0 orders to invoice
2025-06-07 16:16:31.414 +10:00 [INF] Found 0 orders to process
2025-06-07 16:16:31.417 +10:00 [INF] Order processing completed. Processed: 0, Success: 0, Failed: 0
2025-06-07 16:16:31.428 +10:00 [INF] Processing credit invoices...
2025-06-07 16:16:31.441 +10:00 [INF] Getting 10 credits to invoice
2025-06-07 16:16:31.533 +10:00 [INF] Found 0 credits to invoice
2025-06-07 16:16:31.537 +10:00 [INF] Processing refund invoices...
2025-06-07 16:16:31.542 +10:00 [INF] Getting 10 refunds to invoice
2025-06-07 16:16:31.577 +10:00 [INF] Found 0 refunds to invoice
2025-06-07 16:16:31.580 +10:00 [INF] Cleaning up invoicer logs...
2025-06-07 16:16:31.663 +10:00 [INF] LEP Invoicer completed successfully in 1354ms. Orders: 0, Credits: 0, Refunds: 0
2025-06-07 16:16:31.673 +10:00 [INF] Database connection disposed
2025-06-07 16:16:31.676 +10:00 [INF] LEP Invoicer completed with result: 0
2025-06-07 16:20:16.300 +10:00 [INF] Starting LEP Invoicer application
2025-06-07 16:20:19.039 +10:00 [INF] Starting LEP Invoicer at "2025-06-07T16:20:19.0280432+10:00"
2025-06-07 16:20:19.051 +10:00 [INF] Initializing services...
2025-06-07 16:20:19.054 +10:00 [INF] Initializing database connection and NHibernate session...
2025-06-07 16:20:19.060 +10:00 [INF] Database connection established successfully
2025-06-07 16:20:19.066 +10:00 [INF] Initializing MYOB service...
2025-06-07 16:20:19.068 +10:00 [WRN] MYOB service running in TEST MODE - MYOB integration disabled
2025-06-07 16:20:19.081 +10:00 [INF] Services initialized successfully
2025-06-07 16:20:19.086 +10:00 [INF] Processing order invoices...
2025-06-07 16:20:19.091 +10:00 [INF] Getting 20 orders to invoice
2025-06-07 16:20:19.995 +10:00 [INF] Found 0 orders to invoice
2025-06-07 16:20:19.999 +10:00 [INF] Found 0 orders to process
2025-06-07 16:20:20.003 +10:00 [INF] Order processing completed. Processed: 0, Success: 0, Failed: 0
2025-06-07 16:20:20.010 +10:00 [INF] Processing credit invoices...
2025-06-07 16:20:20.016 +10:00 [INF] Getting 10 credits to invoice
2025-06-07 16:20:20.065 +10:00 [INF] Found 0 credits to invoice
2025-06-07 16:20:20.072 +10:00 [INF] Processing refund invoices...
2025-06-07 16:20:20.076 +10:00 [INF] Getting 10 refunds to invoice
2025-06-07 16:20:20.090 +10:00 [INF] Found 0 refunds to invoice
2025-06-07 16:20:20.093 +10:00 [INF] Cleaning up invoicer logs...
2025-06-07 16:20:20.161 +10:00 [INF] LEP Invoicer completed successfully in 1133ms. Orders: 0, Credits: 0, Refunds: 0
2025-06-07 16:20:20.171 +10:00 [INF] Database connection disposed
2025-06-07 16:20:20.174 +10:00 [INF] LEP Invoicer completed with result: 0
2025-06-07 16:28:17.808 +10:00 [INF] Starting LEP Invoicer application
2025-06-07 16:28:20.529 +10:00 [INF] Starting LEP Invoicer at "2025-06-07T16:28:20.5185631+10:00"
2025-06-07 16:28:20.544 +10:00 [INF] Initializing services...
2025-06-07 16:28:20.547 +10:00 [INF] Initializing database connection and NHibernate session...
2025-06-07 16:28:20.553 +10:00 [INF] Database connection established successfully
2025-06-07 16:28:20.562 +10:00 [INF] Initializing MYOB service...
2025-06-07 16:28:20.567 +10:00 [WRN] MYOB service running in TEST MODE - MYOB integration disabled
2025-06-07 16:28:20.583 +10:00 [INF] Services initialized successfully
2025-06-07 16:28:20.596 +10:00 [INF] === CREATING INVOICER2LOG ENTRIES FOR CREDITS ===
2025-06-07 16:28:20.600 +10:00 [INF] --- Processing Type 'C' ---
2025-06-07 16:28:20.651 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1813, Type=C, Amount=(¤69.17), OrderId=1414012
2025-06-07 16:28:20.662 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1811, Type=C, Amount=(¤250.61), OrderId=1402942
2025-06-07 16:28:20.678 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1810, Type=C, Amount=(¤327.51), OrderId=1410502
2025-06-07 16:28:20.684 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1809, Type=C, Amount=(¤124.20), OrderId=1411113
2025-06-07 16:28:20.701 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1808, Type=C, Amount=(¤25.00), OrderId=1413645
2025-06-07 16:28:20.712 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1790, Type=C, Amount=(¤130.15), OrderId=1408377
2025-06-07 16:28:20.718 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1788, Type=C, Amount=(¤255.00), OrderId=1407240
2025-06-07 16:28:20.727 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1773, Type=C, Amount=(¤153.30), OrderId=1400396
2025-06-07 16:28:20.779 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1771, Type=C, Amount=(¤130.50), OrderId=1404661
2025-06-07 16:28:20.791 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1765, Type=C, Amount=(¤139.15), OrderId=1400006
2025-06-07 16:28:20.813 +10:00 [INF]   Created 10 Invoicer2Log entries for type 'C'
2025-06-07 16:28:20.815 +10:00 [INF] --- Processing Type 'M' ---
2025-06-07 16:28:20.823 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1800, Type=M, Amount=¤55.00, OrderId=1406542
2025-06-07 16:28:20.829 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1787, Type=M, Amount=¤10.46, OrderId=1407385
2025-06-07 16:28:20.835 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1768, Type=M, Amount=¤101.25, OrderId=1401426
2025-06-07 16:28:20.854 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1752, Type=M, Amount=¤82.50, OrderId=1392052
2025-06-07 16:28:20.869 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1751, Type=M, Amount=¤23.70, OrderId=1399527
2025-06-07 16:28:20.879 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1748, Type=M, Amount=¤10.46, OrderId=1397180
2025-06-07 16:28:20.884 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1692, Type=M, Amount=¤15.95, OrderId=1388185
2025-06-07 16:28:20.890 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1652, Type=M, Amount=¤42.05, OrderId=1377756
2025-06-07 16:28:20.894 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1650, Type=M, Amount=¤40.91, OrderId=1370803
2025-06-07 16:28:20.898 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1636, Type=M, Amount=¤89.39, OrderId=1374410
2025-06-07 16:28:20.904 +10:00 [INF]   Created 10 Invoicer2Log entries for type 'M'
2025-06-07 16:28:20.911 +10:00 [INF] --- Processing Type 'CI' ---
2025-06-07 16:28:20.921 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1828, Type=CI, Amount=¤36.61, OrderId=1415475
2025-06-07 16:28:20.927 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1827, Type=CI, Amount=¤105.00, OrderId=1411867
2025-06-07 16:28:20.936 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1829, Type=CI, Amount=¤36.61, OrderId=1412841
2025-06-07 16:28:20.943 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1826, Type=CI, Amount=¤67.23, OrderId=1411678
2025-06-07 16:28:20.989 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1825, Type=CI, Amount=¤215.75, OrderId=1412284
2025-06-07 16:28:20.994 +10:00 [INF]   Created 5 Invoicer2Log entries for type 'CI'
2025-06-07 16:28:20.996 +10:00 [INF] --- Processing Type 'S' ---
2025-06-07 16:28:21.008 +10:00 [ERR] Failed to create Invoicer2Log entry for credit 1832
System.Data.SqlClient.SqlException (0x80131904): The INSERT statement conflicted with the FOREIGN KEY constraint "FK__Invoicer2__Order__2CEEE6F6". The conflict occurred in database "PRD_AU", table "dbo.Order", column 'Id'.
The statement has been terminated.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at System.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at System.Data.SqlClient.SqlCommand.EndExecuteNonQueryInternal(IAsyncResult asyncResult)
   at System.Data.SqlClient.SqlCommand.EndExecuteNonQuery(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at LepInvoicer.Services.DatabaseService.CreateInvoicer2LogEntryForCreditAsync(Int32 creditId, String type, Decimal amount, DateTime dateCreated, Nullable`1 orderId, Nullable`1 customerId) in C:\LepSF\au\LepInvoicer\Services\DatabaseService.cs:line 350
ClientConnectionId:35df2186-1dca-4fe2-81a4-bad50e35cf9a
Error Number:547,State:0,Class:16
2025-06-07 16:28:21.050 +10:00 [ERR] Failed to create Invoicer2Log entry for credit 1831
System.Data.SqlClient.SqlException (0x80131904): The INSERT statement conflicted with the FOREIGN KEY constraint "FK__Invoicer2__Order__2CEEE6F6". The conflict occurred in database "PRD_AU", table "dbo.Order", column 'Id'.
The statement has been terminated.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at System.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at System.Data.SqlClient.SqlCommand.EndExecuteNonQueryInternal(IAsyncResult asyncResult)
   at System.Data.SqlClient.SqlCommand.EndExecuteNonQuery(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at LepInvoicer.Services.DatabaseService.CreateInvoicer2LogEntryForCreditAsync(Int32 creditId, String type, Decimal amount, DateTime dateCreated, Nullable`1 orderId, Nullable`1 customerId) in C:\LepSF\au\LepInvoicer\Services\DatabaseService.cs:line 350
ClientConnectionId:35df2186-1dca-4fe2-81a4-bad50e35cf9a
Error Number:547,State:0,Class:16
2025-06-07 16:28:21.107 +10:00 [ERR] Failed to create Invoicer2Log entry for credit 1830
System.Data.SqlClient.SqlException (0x80131904): The INSERT statement conflicted with the FOREIGN KEY constraint "FK__Invoicer2__Order__2CEEE6F6". The conflict occurred in database "PRD_AU", table "dbo.Order", column 'Id'.
The statement has been terminated.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at System.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at System.Data.SqlClient.SqlCommand.EndExecuteNonQueryInternal(IAsyncResult asyncResult)
   at System.Data.SqlClient.SqlCommand.EndExecuteNonQuery(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at LepInvoicer.Services.DatabaseService.CreateInvoicer2LogEntryForCreditAsync(Int32 creditId, String type, Decimal amount, DateTime dateCreated, Nullable`1 orderId, Nullable`1 customerId) in C:\LepSF\au\LepInvoicer\Services\DatabaseService.cs:line 350
ClientConnectionId:35df2186-1dca-4fe2-81a4-bad50e35cf9a
Error Number:547,State:0,Class:16
2025-06-07 16:28:21.153 +10:00 [ERR] Failed to create Invoicer2Log entry for credit 1824
System.Data.SqlClient.SqlException (0x80131904): The INSERT statement conflicted with the FOREIGN KEY constraint "FK__Invoicer2__Order__2CEEE6F6". The conflict occurred in database "PRD_AU", table "dbo.Order", column 'Id'.
The statement has been terminated.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at System.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at System.Data.SqlClient.SqlCommand.EndExecuteNonQueryInternal(IAsyncResult asyncResult)
   at System.Data.SqlClient.SqlCommand.EndExecuteNonQuery(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at LepInvoicer.Services.DatabaseService.CreateInvoicer2LogEntryForCreditAsync(Int32 creditId, String type, Decimal amount, DateTime dateCreated, Nullable`1 orderId, Nullable`1 customerId) in C:\LepSF\au\LepInvoicer\Services\DatabaseService.cs:line 350
ClientConnectionId:35df2186-1dca-4fe2-81a4-bad50e35cf9a
Error Number:547,State:0,Class:16
2025-06-07 16:28:21.203 +10:00 [ERR] Failed to create Invoicer2Log entry for credit 1818
System.Data.SqlClient.SqlException (0x80131904): The INSERT statement conflicted with the FOREIGN KEY constraint "FK__Invoicer2__Order__2CEEE6F6". The conflict occurred in database "PRD_AU", table "dbo.Order", column 'Id'.
The statement has been terminated.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at System.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at System.Data.SqlClient.SqlCommand.EndExecuteNonQueryInternal(IAsyncResult asyncResult)
   at System.Data.SqlClient.SqlCommand.EndExecuteNonQuery(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at LepInvoicer.Services.DatabaseService.CreateInvoicer2LogEntryForCreditAsync(Int32 creditId, String type, Decimal amount, DateTime dateCreated, Nullable`1 orderId, Nullable`1 customerId) in C:\LepSF\au\LepInvoicer\Services\DatabaseService.cs:line 350
ClientConnectionId:35df2186-1dca-4fe2-81a4-bad50e35cf9a
Error Number:547,State:0,Class:16
2025-06-07 16:28:21.216 +10:00 [ERR] Failed to create Invoicer2Log entry for credit 1817
System.Data.SqlClient.SqlException (0x80131904): The INSERT statement conflicted with the FOREIGN KEY constraint "FK__Invoicer2__Order__2CEEE6F6". The conflict occurred in database "PRD_AU", table "dbo.Order", column 'Id'.
The statement has been terminated.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at System.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at System.Data.SqlClient.SqlCommand.EndExecuteNonQueryInternal(IAsyncResult asyncResult)
   at System.Data.SqlClient.SqlCommand.EndExecuteNonQuery(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at LepInvoicer.Services.DatabaseService.CreateInvoicer2LogEntryForCreditAsync(Int32 creditId, String type, Decimal amount, DateTime dateCreated, Nullable`1 orderId, Nullable`1 customerId) in C:\LepSF\au\LepInvoicer\Services\DatabaseService.cs:line 350
ClientConnectionId:35df2186-1dca-4fe2-81a4-bad50e35cf9a
Error Number:547,State:0,Class:16
2025-06-07 16:28:21.238 +10:00 [ERR] Failed to create Invoicer2Log entry for credit 1816
System.Data.SqlClient.SqlException (0x80131904): The INSERT statement conflicted with the FOREIGN KEY constraint "FK__Invoicer2__Order__2CEEE6F6". The conflict occurred in database "PRD_AU", table "dbo.Order", column 'Id'.
The statement has been terminated.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at System.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at System.Data.SqlClient.SqlCommand.EndExecuteNonQueryInternal(IAsyncResult asyncResult)
   at System.Data.SqlClient.SqlCommand.EndExecuteNonQuery(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at LepInvoicer.Services.DatabaseService.CreateInvoicer2LogEntryForCreditAsync(Int32 creditId, String type, Decimal amount, DateTime dateCreated, Nullable`1 orderId, Nullable`1 customerId) in C:\LepSF\au\LepInvoicer\Services\DatabaseService.cs:line 350
ClientConnectionId:35df2186-1dca-4fe2-81a4-bad50e35cf9a
Error Number:547,State:0,Class:16
2025-06-07 16:28:21.256 +10:00 [ERR] Failed to create Invoicer2Log entry for credit 1815
System.Data.SqlClient.SqlException (0x80131904): The INSERT statement conflicted with the FOREIGN KEY constraint "FK__Invoicer2__Order__2CEEE6F6". The conflict occurred in database "PRD_AU", table "dbo.Order", column 'Id'.
The statement has been terminated.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at System.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at System.Data.SqlClient.SqlCommand.EndExecuteNonQueryInternal(IAsyncResult asyncResult)
   at System.Data.SqlClient.SqlCommand.EndExecuteNonQuery(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at LepInvoicer.Services.DatabaseService.CreateInvoicer2LogEntryForCreditAsync(Int32 creditId, String type, Decimal amount, DateTime dateCreated, Nullable`1 orderId, Nullable`1 customerId) in C:\LepSF\au\LepInvoicer\Services\DatabaseService.cs:line 350
ClientConnectionId:35df2186-1dca-4fe2-81a4-bad50e35cf9a
Error Number:547,State:0,Class:16
2025-06-07 16:28:21.284 +10:00 [ERR] Failed to create Invoicer2Log entry for credit 1814
System.Data.SqlClient.SqlException (0x80131904): The INSERT statement conflicted with the FOREIGN KEY constraint "FK__Invoicer2__Order__2CEEE6F6". The conflict occurred in database "PRD_AU", table "dbo.Order", column 'Id'.
The statement has been terminated.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at System.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at System.Data.SqlClient.SqlCommand.EndExecuteNonQueryInternal(IAsyncResult asyncResult)
   at System.Data.SqlClient.SqlCommand.EndExecuteNonQuery(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at LepInvoicer.Services.DatabaseService.CreateInvoicer2LogEntryForCreditAsync(Int32 creditId, String type, Decimal amount, DateTime dateCreated, Nullable`1 orderId, Nullable`1 customerId) in C:\LepSF\au\LepInvoicer\Services\DatabaseService.cs:line 350
ClientConnectionId:35df2186-1dca-4fe2-81a4-bad50e35cf9a
Error Number:547,State:0,Class:16
2025-06-07 16:28:21.326 +10:00 [ERR] Failed to create Invoicer2Log entry for credit 1812
System.Data.SqlClient.SqlException (0x80131904): The INSERT statement conflicted with the FOREIGN KEY constraint "FK__Invoicer2__Order__2CEEE6F6". The conflict occurred in database "PRD_AU", table "dbo.Order", column 'Id'.
The statement has been terminated.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at System.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at System.Data.SqlClient.SqlCommand.EndExecuteNonQueryInternal(IAsyncResult asyncResult)
   at System.Data.SqlClient.SqlCommand.EndExecuteNonQuery(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at LepInvoicer.Services.DatabaseService.CreateInvoicer2LogEntryForCreditAsync(Int32 creditId, String type, Decimal amount, DateTime dateCreated, Nullable`1 orderId, Nullable`1 customerId) in C:\LepSF\au\LepInvoicer\Services\DatabaseService.cs:line 350
ClientConnectionId:35df2186-1dca-4fe2-81a4-bad50e35cf9a
Error Number:547,State:0,Class:16
2025-06-07 16:28:21.340 +10:00 [INF]   Created 10 Invoicer2Log entries for type 'S'
2025-06-07 16:28:21.342 +10:00 [INF] === INVOICER2LOG CREATION COMPLETE ===
2025-06-07 16:28:21.346 +10:00 [INF] Processing order invoices...
2025-06-07 16:28:21.350 +10:00 [INF] Getting 20 orders to invoice
2025-06-07 16:28:22.243 +10:00 [INF] Found 0 orders to invoice
2025-06-07 16:28:22.246 +10:00 [INF] Found 0 orders to process
2025-06-07 16:28:22.252 +10:00 [INF] Order processing completed. Processed: 0, Success: 0, Failed: 0
2025-06-07 16:28:22.257 +10:00 [INF] Processing credit invoices...
2025-06-07 16:28:22.260 +10:00 [INF] Getting 10 credits to invoice
2025-06-07 16:28:22.310 +10:00 [INF] Found 0 credits to invoice
2025-06-07 16:28:22.315 +10:00 [INF] Processing refund invoices...
2025-06-07 16:28:22.319 +10:00 [INF] Getting 10 refunds to invoice
2025-06-07 16:28:22.347 +10:00 [INF] Found 0 refunds to invoice
2025-06-07 16:28:22.349 +10:00 [INF] Cleaning up invoicer logs...
2025-06-07 16:28:22.434 +10:00 [INF] LEP Invoicer completed successfully in 1915ms. Orders: 0, Credits: 0, Refunds: 0
2025-06-07 16:28:22.445 +10:00 [INF] Database connection disposed
2025-06-07 16:28:22.460 +10:00 [INF] LEP Invoicer completed with result: 0
2025-06-07 16:35:10.986 +10:00 [INF] Starting LEP Invoicer application
2025-06-07 16:35:13.653 +10:00 [INF] Starting LEP Invoicer at "2025-06-07T16:35:13.6389585+10:00"
2025-06-07 16:35:13.667 +10:00 [INF] Initializing services...
2025-06-07 16:35:13.670 +10:00 [INF] Initializing database connection and NHibernate session...
2025-06-07 16:35:13.681 +10:00 [INF] Database connection established successfully
2025-06-07 16:35:13.691 +10:00 [INF] Initializing MYOB service...
2025-06-07 16:35:13.696 +10:00 [WRN] MYOB service running in TEST MODE - MYOB integration disabled
2025-06-07 16:35:13.720 +10:00 [INF] Services initialized successfully
2025-06-07 16:35:13.730 +10:00 [INF] === CREATING INVOICER2LOG ENTRIES FOR CREDITS ===
2025-06-07 16:35:13.733 +10:00 [INF] --- Processing Type 'C' ---
2025-06-07 16:35:13.781 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1813, Type=C, Amount=(¤69.17), OrderId=1414012, CustomerId=14665
2025-06-07 16:35:13.825 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1811, Type=C, Amount=(¤250.61), OrderId=1402942, CustomerId=159884
2025-06-07 16:35:13.832 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1810, Type=C, Amount=(¤327.51), OrderId=1410502, CustomerId=17820
2025-06-07 16:35:13.840 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1809, Type=C, Amount=(¤124.20), OrderId=1411113, CustomerId=16263
2025-06-07 16:35:13.846 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1808, Type=C, Amount=(¤25.00), OrderId=1413645, CustomerId=21993
2025-06-07 16:35:13.855 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1790, Type=C, Amount=(¤130.15), OrderId=1408377, CustomerId=14719
2025-06-07 16:35:13.860 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1788, Type=C, Amount=(¤255.00), OrderId=1407240, CustomerId=136256
2025-06-07 16:35:13.872 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1773, Type=C, Amount=(¤153.30), OrderId=1400396, CustomerId=25195
2025-06-07 16:35:13.921 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1771, Type=C, Amount=(¤130.50), OrderId=1404661, CustomerId=136528
2025-06-07 16:35:13.929 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1765, Type=C, Amount=(¤139.15), OrderId=1400006, CustomerId=13996
2025-06-07 16:35:13.984 +10:00 [INF]   Created 10 Invoicer2Log entries for type 'C'
2025-06-07 16:35:13.986 +10:00 [INF] --- Processing Type 'M' ---
2025-06-07 16:35:13.999 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1800, Type=M, Amount=¤55.00, OrderId=1406542, CustomerId=161563
2025-06-07 16:35:14.044 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1787, Type=M, Amount=¤10.46, OrderId=1407385, CustomerId=153164
2025-06-07 16:35:14.051 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1768, Type=M, Amount=¤101.25, OrderId=1401426, CustomerId=136825
2025-06-07 16:35:14.060 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1752, Type=M, Amount=¤82.50, OrderId=1392052, CustomerId=24822
2025-06-07 16:35:14.101 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1751, Type=M, Amount=¤23.70, OrderId=1399527, CustomerId=25461
2025-06-07 16:35:14.107 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1748, Type=M, Amount=¤10.46, OrderId=1397180, CustomerId=19633
2025-06-07 16:35:14.112 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1692, Type=M, Amount=¤15.95, OrderId=1388185, CustomerId=14882
2025-06-07 16:35:14.120 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1652, Type=M, Amount=¤42.05, OrderId=1377756, CustomerId=136336
2025-06-07 16:35:14.163 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1650, Type=M, Amount=¤40.91, OrderId=1370803, CustomerId=136154
2025-06-07 16:35:14.170 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1636, Type=M, Amount=¤89.39, OrderId=1374410, CustomerId=34676
2025-06-07 16:35:14.175 +10:00 [INF]   Created 10 Invoicer2Log entries for type 'M'
2025-06-07 16:35:14.177 +10:00 [INF] --- Processing Type 'CI' ---
2025-06-07 16:35:14.182 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1828, Type=CI, Amount=¤36.61, OrderId=1415475, CustomerId=161528
2025-06-07 16:35:14.228 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1827, Type=CI, Amount=¤105.00, OrderId=1411867, CustomerId=161576
2025-06-07 16:35:14.233 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1829, Type=CI, Amount=¤36.61, OrderId=1412841, CustomerId=161528
2025-06-07 16:35:14.239 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1826, Type=CI, Amount=¤67.23, OrderId=1411678, CustomerId=161569
2025-06-07 16:35:14.245 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1825, Type=CI, Amount=¤215.75, OrderId=1412284, CustomerId=161569
2025-06-07 16:35:14.284 +10:00 [INF]   Created 5 Invoicer2Log entries for type 'CI'
2025-06-07 16:35:14.291 +10:00 [INF] --- Processing Type 'S' ---
2025-06-07 16:35:14.298 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1832, Type=S, Amount=¤4.28, OrderId=NULL, CustomerId=15164
2025-06-07 16:35:14.351 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1831, Type=S, Amount=¤114.97, OrderId=NULL, CustomerId=158666
2025-06-07 16:35:14.358 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1830, Type=S, Amount=¤114.97, OrderId=NULL, CustomerId=158666
2025-06-07 16:35:14.368 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1824, Type=S, Amount=¤70.04, OrderId=NULL, CustomerId=18987
2025-06-07 16:35:14.411 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1818, Type=S, Amount=¤61.92, OrderId=NULL, CustomerId=153179
2025-06-07 16:35:14.422 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1817, Type=S, Amount=¤19.84, OrderId=NULL, CustomerId=14370
2025-06-07 16:35:14.458 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1816, Type=S, Amount=¤6.80, OrderId=NULL, CustomerId=14370
2025-06-07 16:35:14.464 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1815, Type=S, Amount=¤3.83, OrderId=NULL, CustomerId=13899
2025-06-07 16:35:14.476 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1814, Type=S, Amount=¤1.37, OrderId=NULL, CustomerId=13751
2025-06-07 16:35:14.496 +10:00 [INF]   Created Invoicer2Log entry: CreditId=1812, Type=S, Amount=¤1,312.65, OrderId=NULL, CustomerId=25195
2025-06-07 16:35:14.511 +10:00 [INF]   Created 10 Invoicer2Log entries for type 'S'
2025-06-07 16:35:14.514 +10:00 [INF] === INVOICER2LOG CREATION COMPLETE ===
2025-06-07 16:35:14.518 +10:00 [INF] Processing order invoices...
2025-06-07 16:35:14.524 +10:00 [INF] Getting 20 orders to invoice
2025-06-07 16:35:15.445 +10:00 [INF] Found 0 orders to invoice
2025-06-07 16:35:15.451 +10:00 [INF] Found 0 orders to process
2025-06-07 16:35:15.454 +10:00 [INF] Order processing completed. Processed: 0, Success: 0, Failed: 0
2025-06-07 16:35:15.460 +10:00 [INF] Processing credit invoices...
2025-06-07 16:35:15.464 +10:00 [INF] Getting 10 credits to invoice
2025-06-07 16:35:15.544 +10:00 [INF] Found 0 credits to invoice
2025-06-07 16:35:15.585 +10:00 [INF] Processing refund invoices...
2025-06-07 16:35:15.588 +10:00 [INF] Getting 10 refunds to invoice
2025-06-07 16:35:15.602 +10:00 [INF] Found 0 refunds to invoice
2025-06-07 16:35:15.604 +10:00 [INF] Cleaning up invoicer logs...
2025-06-07 16:35:15.682 +10:00 [INF] LEP Invoicer completed successfully in 2043ms. Orders: 0, Credits: 0, Refunds: 0
2025-06-07 16:35:15.688 +10:00 [INF] Database connection disposed
2025-06-07 16:35:15.690 +10:00 [INF] LEP Invoicer completed with result: 0
