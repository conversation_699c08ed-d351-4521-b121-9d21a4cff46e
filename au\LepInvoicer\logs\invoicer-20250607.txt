2025-06-07 15:17:41.211 +10:00 [INF] Starting LEP Invoicer application
2025-06-07 15:17:44.613 +10:00 [INF] Starting LEP Invoicer at "2025-06-07T15:17:44.6009426+10:00"
2025-06-07 15:17:44.650 +10:00 [INF] Initializing services...
2025-06-07 15:17:44.654 +10:00 [INF] Initializing database connection and NHibernate session...
2025-06-07 15:17:44.661 +10:00 [INF] Database connection established successfully
2025-06-07 15:17:44.711 +10:00 [INF] Initializing MYOB service...
2025-06-07 15:17:44.984 +10:00 [ERR] Failed to initialize MYOB service
MYOB.AccountRight.SDK.ApiCommunicationException: Encountered a communication error (https://api.myob.com/accountright)
 ---> System.Net.WebException: The remote server returned an error: (401) Unauthorized.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiGetRequestSync[T](Uri uri, ICompanyFileCredentials credentials, String eTag)
   at MYOB.AccountRight.SDK.Services.CompanyFileService.GetRange()
   at LepInvoicer.Services.MYOBService.<>c__DisplayClass15_0.<InitializeAsync>b__1() in C:\LepSF\au\LepInvoicer\Services\MYOBService.cs:line 70
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Services.MYOBService.InitializeAsync() in C:\LepSF\au\LepInvoicer\Services\MYOBService.cs:line 70
2025-06-07 15:17:45.028 +10:00 [ERR] LEP Invoicer failed after 427ms
MYOB.AccountRight.SDK.ApiCommunicationException: Encountered a communication error (https://api.myob.com/accountright)
 ---> System.Net.WebException: The remote server returned an error: (401) Unauthorized.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiGetRequestSync[T](Uri uri, ICompanyFileCredentials credentials, String eTag)
   at MYOB.AccountRight.SDK.Services.CompanyFileService.GetRange()
   at LepInvoicer.Services.MYOBService.<>c__DisplayClass15_0.<InitializeAsync>b__1() in C:\LepSF\au\LepInvoicer\Services\MYOBService.cs:line 70
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Services.MYOBService.InitializeAsync() in C:\LepSF\au\LepInvoicer\Services\MYOBService.cs:line 70
   at LepInvoicer.Services.InvoicerService.InitializeServicesAsync() in C:\LepSF\au\LepInvoicer\Services\InvoicerService.cs:line 82
   at LepInvoicer.Services.InvoicerService.RunInvoicerAsync() in C:\LepSF\au\LepInvoicer\Services\InvoicerService.cs:line 45
2025-06-07 15:17:45.064 +10:00 [INF] Database connection disposed
2025-06-07 15:17:45.076 +10:00 [INF] LEP Invoicer completed with result: 1
2025-06-07 15:18:41.236 +10:00 [INF] Starting LEP Invoicer application
2025-06-07 15:18:44.062 +10:00 [INF] Starting LEP Invoicer at "2025-06-07T15:18:44.0503842+10:00"
2025-06-07 15:18:44.082 +10:00 [INF] About to initialize services...
2025-06-07 15:18:44.093 +10:00 [INF] Initializing services...
2025-06-07 15:18:44.095 +10:00 [INF] Initializing database service...
2025-06-07 15:18:44.099 +10:00 [INF] Initializing database connection and NHibernate session...
2025-06-07 15:18:44.107 +10:00 [INF] Database connection established successfully
2025-06-07 15:18:44.111 +10:00 [INF] Database service initialized successfully
2025-06-07 15:18:44.114 +10:00 [INF] Initializing MYOB service...
2025-06-07 15:18:44.120 +10:00 [INF] Initializing MYOB service...
2025-06-07 15:18:44.356 +10:00 [ERR] Failed to initialize MYOB service
MYOB.AccountRight.SDK.ApiCommunicationException: Encountered a communication error (https://api.myob.com/accountright)
 ---> System.Net.WebException: The remote server returned an error: (401) Unauthorized.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiGetRequestSync[T](Uri uri, ICompanyFileCredentials credentials, String eTag)
   at MYOB.AccountRight.SDK.Services.CompanyFileService.GetRange()
   at LepInvoicer.Services.MYOBService.<>c__DisplayClass15_0.<InitializeAsync>b__1() in C:\LepSF\au\LepInvoicer\Services\MYOBService.cs:line 70
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Services.MYOBService.InitializeAsync() in C:\LepSF\au\LepInvoicer\Services\MYOBService.cs:line 70
2025-06-07 15:18:44.402 +10:00 [ERR] LEP Invoicer failed after 351ms
MYOB.AccountRight.SDK.ApiCommunicationException: Encountered a communication error (https://api.myob.com/accountright)
 ---> System.Net.WebException: The remote server returned an error: (401) Unauthorized.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiGetRequestSync[T](Uri uri, ICompanyFileCredentials credentials, String eTag)
   at MYOB.AccountRight.SDK.Services.CompanyFileService.GetRange()
   at LepInvoicer.Services.MYOBService.<>c__DisplayClass15_0.<InitializeAsync>b__1() in C:\LepSF\au\LepInvoicer\Services\MYOBService.cs:line 70
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Services.MYOBService.InitializeAsync() in C:\LepSF\au\LepInvoicer\Services\MYOBService.cs:line 70
   at LepInvoicer.Services.InvoicerService.InitializeServicesAsync() in C:\LepSF\au\LepInvoicer\Services\InvoicerService.cs:line 91
   at LepInvoicer.Services.InvoicerService.RunInvoicerAsync() in C:\LepSF\au\LepInvoicer\Services\InvoicerService.cs:line 49
2025-06-07 15:18:44.429 +10:00 [INF] Database connection disposed
2025-06-07 15:18:44.434 +10:00 [INF] LEP Invoicer completed with result: 1
2025-06-07 15:19:57.166 +10:00 [INF] Starting LEP Invoicer application
2025-06-07 15:19:59.813 +10:00 [INF] Starting LEP Invoicer at "2025-06-07T15:19:59.8014997+10:00"
2025-06-07 15:19:59.823 +10:00 [INF] About to initialize services...
2025-06-07 15:19:59.828 +10:00 [INF] Initializing services...
2025-06-07 15:19:59.829 +10:00 [INF] Initializing database service...
2025-06-07 15:19:59.836 +10:00 [INF] Initializing database connection and NHibernate session...
2025-06-07 15:19:59.843 +10:00 [INF] Database connection established successfully
2025-06-07 15:19:59.846 +10:00 [INF] Database service initialized successfully
2025-06-07 15:19:59.847 +10:00 [INF] Initializing MYOB service...
2025-06-07 15:19:59.854 +10:00 [INF] Initializing MYOB service...
2025-06-07 15:19:59.859 +10:00 [INF] Using username/password authentication
2025-06-07 15:20:00.113 +10:00 [ERR] Failed to initialize MYOB service
MYOB.AccountRight.SDK.ApiCommunicationException: Encountered a communication error (https://api.myob.com/accountright)
 ---> System.Net.WebException: The remote server returned an error: (401) Unauthorized.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiGetRequestSync[T](Uri uri, ICompanyFileCredentials credentials, String eTag)
   at MYOB.AccountRight.SDK.Services.CompanyFileService.GetRange()
   at LepInvoicer.Services.MYOBService.<>c__DisplayClass15_0.<InitializeAsync>b__1() in C:\LepSF\au\LepInvoicer\Services\MYOBService.cs:line 76
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Services.MYOBService.InitializeAsync() in C:\LepSF\au\LepInvoicer\Services\MYOBService.cs:line 76
2025-06-07 15:20:00.156 +10:00 [ERR] LEP Invoicer failed after 354ms
MYOB.AccountRight.SDK.ApiCommunicationException: Encountered a communication error (https://api.myob.com/accountright)
 ---> System.Net.WebException: The remote server returned an error: (401) Unauthorized.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiGetRequestSync[T](Uri uri, ICompanyFileCredentials credentials, String eTag)
   at MYOB.AccountRight.SDK.Services.CompanyFileService.GetRange()
   at LepInvoicer.Services.MYOBService.<>c__DisplayClass15_0.<InitializeAsync>b__1() in C:\LepSF\au\LepInvoicer\Services\MYOBService.cs:line 76
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Services.MYOBService.InitializeAsync() in C:\LepSF\au\LepInvoicer\Services\MYOBService.cs:line 76
   at LepInvoicer.Services.InvoicerService.InitializeServicesAsync() in C:\LepSF\au\LepInvoicer\Services\InvoicerService.cs:line 91
   at LepInvoicer.Services.InvoicerService.RunInvoicerAsync() in C:\LepSF\au\LepInvoicer\Services\InvoicerService.cs:line 49
2025-06-07 15:20:00.178 +10:00 [INF] Database connection disposed
2025-06-07 15:20:00.181 +10:00 [INF] LEP Invoicer completed with result: 1
2025-06-07 15:21:49.461 +10:00 [INF] Starting LEP Invoicer application
2025-06-07 15:21:52.210 +10:00 [INF] Starting LEP Invoicer at "2025-06-07T15:21:52.1987476+10:00"
2025-06-07 15:21:52.221 +10:00 [INF] About to initialize services...
2025-06-07 15:21:52.228 +10:00 [INF] Initializing services...
2025-06-07 15:21:52.231 +10:00 [INF] Initializing database service...
2025-06-07 15:21:52.237 +10:00 [INF] Initializing database connection and NHibernate session...
2025-06-07 15:21:52.246 +10:00 [INF] Database connection established successfully
2025-06-07 15:21:52.249 +10:00 [INF] Database service initialized successfully
2025-06-07 15:21:52.250 +10:00 [INF] Initializing MYOB service...
2025-06-07 15:21:52.258 +10:00 [INF] Initializing MYOB service...
2025-06-07 15:21:52.259 +10:00 [WRN] MYOB service running in TEST MODE - MYOB integration disabled
2025-06-07 15:21:52.261 +10:00 [INF] MYOB service initialized successfully
2025-06-07 15:21:52.262 +10:00 [INF] Setting up FastReport...
2025-06-07 15:21:53.376 +10:00 [INF] FastReport setup completed
2025-06-07 15:21:53.378 +10:00 [INF] All services initialized successfully
2025-06-07 15:21:53.382 +10:00 [INF] Services initialization completed
2025-06-07 15:21:53.389 +10:00 [INF] Processing order invoices...
2025-06-07 15:21:53.397 +10:00 [INF] Getting 20 orders to invoice
2025-06-07 15:21:54.784 +10:00 [INF] Found 0 orders to invoice
2025-06-07 15:21:54.787 +10:00 [INF] Found 0 orders to process
2025-06-07 15:21:54.791 +10:00 [INF] Order processing completed. Processed: 0, Success: 0, Failed: 0
2025-06-07 15:21:54.797 +10:00 [INF] Processing credit invoices...
2025-06-07 15:21:54.799 +10:00 [INF] Getting 10 credits to invoice
2025-06-07 15:21:55.058 +10:00 [INF] Found 9 credits to invoice
2025-06-07 15:21:55.063 +10:00 [INF] TEST MODE: Simulating MYOB credit invoice creation for credit 1800
2025-06-07 15:21:55.071 +10:00 [INF] TEST MODE: Simulating MYOB credit invoice creation for credit 1825
2025-06-07 15:21:55.078 +10:00 [INF] TEST MODE: Simulating MYOB credit invoice creation for credit 1826
2025-06-07 15:21:55.085 +10:00 [INF] TEST MODE: Simulating MYOB credit invoice creation for credit 1827
2025-06-07 15:21:55.090 +10:00 [INF] TEST MODE: Simulating MYOB credit invoice creation for credit 1828
2025-06-07 15:21:55.096 +10:00 [INF] TEST MODE: Simulating MYOB credit invoice creation for credit 1829
2025-06-07 15:21:55.100 +10:00 [INF] TEST MODE: Simulating MYOB credit invoice creation for credit 1830
2025-06-07 15:21:55.106 +10:00 [INF] TEST MODE: Simulating MYOB credit invoice creation for credit 1831
2025-06-07 15:21:55.109 +10:00 [INF] TEST MODE: Simulating MYOB credit invoice creation for credit 1832
2025-06-07 15:21:55.115 +10:00 [INF] Processing refund invoices...
2025-06-07 15:21:55.120 +10:00 [INF] Getting 10 refunds to invoice
2025-06-07 15:21:55.138 +10:00 [INF] Found 0 refunds to invoice
2025-06-07 15:21:55.141 +10:00 [INF] Cleaning up invoicer logs...
2025-06-07 15:21:55.217 +10:00 [INF] LEP Invoicer completed successfully in 3018ms. Orders: 0, Credits: 9, Refunds: 0
2025-06-07 15:21:55.228 +10:00 [INF] Database connection disposed
2025-06-07 15:21:55.230 +10:00 [INF] LEP Invoicer completed with result: 0
2025-06-07 15:26:46.624 +10:00 [INF] Starting LEP Invoicer application
2025-06-07 15:26:49.179 +10:00 [INF] Starting LEP Invoicer at "2025-06-07T15:26:49.1678500+10:00"
2025-06-07 15:26:49.191 +10:00 [INF] About to initialize services...
2025-06-07 15:26:49.195 +10:00 [INF] Initializing services...
2025-06-07 15:26:49.197 +10:00 [INF] Initializing database service...
2025-06-07 15:26:49.199 +10:00 [INF] Initializing database connection and NHibernate session...
2025-06-07 15:26:49.211 +10:00 [INF] Database connection established successfully
2025-06-07 15:26:49.212 +10:00 [INF] Database service initialized successfully
2025-06-07 15:26:49.214 +10:00 [INF] Initializing MYOB service...
2025-06-07 15:26:49.224 +10:00 [INF] Initializing MYOB service...
2025-06-07 15:26:49.228 +10:00 [INF] MYOB Configuration Debug:
2025-06-07 15:26:49.231 +10:00 [INF]   DeveloperKey: [SET - Length: 36]
2025-06-07 15:26:49.237 +10:00 [INF]   DeveloperSecret: [SET - Length: 24]
2025-06-07 15:26:49.250 +10:00 [INF]   ConfirmationUrl: 'http://desktop'
2025-06-07 15:26:49.253 +10:00 [INF]   CompanyFileName: 'LEP Colour Printers Pty Ltd'
2025-06-07 15:26:49.258 +10:00 [INF]   Username: [SET]
2025-06-07 15:26:49.261 +10:00 [INF]   Password: [SET]
2025-06-07 15:26:49.266 +10:00 [INF] Using username/password authentication
2025-06-07 15:26:49.505 +10:00 [ERR] Failed to initialize MYOB service
MYOB.AccountRight.SDK.ApiCommunicationException: Encountered a communication error (https://api.myob.com/accountright)
 ---> System.Net.WebException: The remote server returned an error: (401) Unauthorized.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiGetRequestSync[T](Uri uri, ICompanyFileCredentials credentials, String eTag)
   at MYOB.AccountRight.SDK.Services.CompanyFileService.GetRange()
   at LepInvoicer.Services.MYOBService.<>c__DisplayClass15_0.<InitializeAsync>b__1() in C:\LepSF\au\LepInvoicer\Services\MYOBService.cs:line 85
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Services.MYOBService.InitializeAsync() in C:\LepSF\au\LepInvoicer\Services\MYOBService.cs:line 85
2025-06-07 15:26:49.544 +10:00 [ERR] LEP Invoicer failed after 376ms
MYOB.AccountRight.SDK.ApiCommunicationException: Encountered a communication error (https://api.myob.com/accountright)
 ---> System.Net.WebException: The remote server returned an error: (401) Unauthorized.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiGetRequestSync[T](Uri uri, ICompanyFileCredentials credentials, String eTag)
   at MYOB.AccountRight.SDK.Services.CompanyFileService.GetRange()
   at LepInvoicer.Services.MYOBService.<>c__DisplayClass15_0.<InitializeAsync>b__1() in C:\LepSF\au\LepInvoicer\Services\MYOBService.cs:line 85
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Services.MYOBService.InitializeAsync() in C:\LepSF\au\LepInvoicer\Services\MYOBService.cs:line 85
   at LepInvoicer.Services.InvoicerService.InitializeServicesAsync() in C:\LepSF\au\LepInvoicer\Services\InvoicerService.cs:line 91
   at LepInvoicer.Services.InvoicerService.RunInvoicerAsync() in C:\LepSF\au\LepInvoicer\Services\InvoicerService.cs:line 49
2025-06-07 15:26:49.573 +10:00 [INF] Database connection disposed
2025-06-07 15:26:49.578 +10:00 [INF] LEP Invoicer completed with result: 1
2025-06-07 15:27:19.697 +10:00 [INF] Starting LEP Invoicer application
2025-06-07 15:27:20.455 +10:00 [INF] MYOBService constructor - Configuration loaded. TestMode: false
2025-06-07 15:27:20.464 +10:00 [INF] MYOBService constructor - MYOB DeveloperKey: [SET]
2025-06-07 15:27:22.319 +10:00 [INF] Starting LEP Invoicer at "2025-06-07T15:27:22.3192651+10:00"
2025-06-07 15:27:22.324 +10:00 [INF] About to initialize services...
2025-06-07 15:27:22.329 +10:00 [INF] Initializing services...
2025-06-07 15:27:22.330 +10:00 [INF] Initializing database service...
2025-06-07 15:27:22.332 +10:00 [INF] Initializing database connection and NHibernate session...
2025-06-07 15:27:22.338 +10:00 [INF] Database connection established successfully
2025-06-07 15:27:22.339 +10:00 [INF] Database service initialized successfully
2025-06-07 15:27:22.340 +10:00 [INF] Initializing MYOB service...
2025-06-07 15:27:22.349 +10:00 [INF] Initializing MYOB service...
2025-06-07 15:27:22.350 +10:00 [INF] MYOB Configuration Debug:
2025-06-07 15:27:22.354 +10:00 [INF]   DeveloperKey: [SET - Length: 36]
2025-06-07 15:27:22.358 +10:00 [INF]   DeveloperSecret: [SET - Length: 24]
2025-06-07 15:27:22.362 +10:00 [INF]   ConfirmationUrl: 'http://desktop'
2025-06-07 15:27:22.370 +10:00 [INF]   CompanyFileName: 'LEP Colour Printers Pty Ltd'
2025-06-07 15:27:22.376 +10:00 [INF]   Username: [SET]
2025-06-07 15:27:22.380 +10:00 [INF]   Password: [SET]
2025-06-07 15:27:22.396 +10:00 [INF] Using username/password authentication
2025-06-07 15:27:22.619 +10:00 [ERR] Failed to initialize MYOB service
MYOB.AccountRight.SDK.ApiCommunicationException: Encountered a communication error (https://api.myob.com/accountright)
 ---> System.Net.WebException: The remote server returned an error: (401) Unauthorized.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiGetRequestSync[T](Uri uri, ICompanyFileCredentials credentials, String eTag)
   at MYOB.AccountRight.SDK.Services.CompanyFileService.GetRange()
   at LepInvoicer.Services.MYOBService.<>c__DisplayClass15_0.<InitializeAsync>b__1() in C:\LepSF\au\LepInvoicer\Services\MYOBService.cs:line 90
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Services.MYOBService.InitializeAsync() in C:\LepSF\au\LepInvoicer\Services\MYOBService.cs:line 90
2025-06-07 15:27:22.652 +10:00 [ERR] LEP Invoicer failed after 332ms
MYOB.AccountRight.SDK.ApiCommunicationException: Encountered a communication error (https://api.myob.com/accountright)
 ---> System.Net.WebException: The remote server returned an error: (401) Unauthorized.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiGetRequestSync[T](Uri uri, ICompanyFileCredentials credentials, String eTag)
   at MYOB.AccountRight.SDK.Services.CompanyFileService.GetRange()
   at LepInvoicer.Services.MYOBService.<>c__DisplayClass15_0.<InitializeAsync>b__1() in C:\LepSF\au\LepInvoicer\Services\MYOBService.cs:line 90
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Services.MYOBService.InitializeAsync() in C:\LepSF\au\LepInvoicer\Services\MYOBService.cs:line 90
   at LepInvoicer.Services.InvoicerService.InitializeServicesAsync() in C:\LepSF\au\LepInvoicer\Services\InvoicerService.cs:line 91
   at LepInvoicer.Services.InvoicerService.RunInvoicerAsync() in C:\LepSF\au\LepInvoicer\Services\InvoicerService.cs:line 49
2025-06-07 15:27:22.681 +10:00 [INF] Database connection disposed
2025-06-07 15:27:22.685 +10:00 [INF] LEP Invoicer completed with result: 1
2025-06-07 15:27:44.234 +10:00 [INF] Starting LEP Invoicer application
2025-06-07 15:27:44.325 +10:00 [INF] Creating host builder...
2025-06-07 15:27:44.333 +10:00 [INF] Building host...
2025-06-07 15:27:45.028 +10:00 [INF] Getting invoicer service...
2025-06-07 15:27:45.162 +10:00 [INF] MYOBService constructor - Configuration loaded. TestMode: false
2025-06-07 15:27:45.173 +10:00 [INF] MYOBService constructor - MYOB DeveloperKey: [SET]
2025-06-07 15:27:47.148 +10:00 [INF] Running invoicer...
2025-06-07 15:27:47.151 +10:00 [INF] Starting LEP Invoicer at "2025-06-07T15:27:47.1514663+10:00"
2025-06-07 15:27:47.156 +10:00 [INF] About to initialize services...
2025-06-07 15:27:47.162 +10:00 [INF] Initializing services...
2025-06-07 15:27:47.163 +10:00 [INF] Initializing database service...
2025-06-07 15:27:47.165 +10:00 [INF] Initializing database connection and NHibernate session...
2025-06-07 15:27:47.173 +10:00 [INF] Database connection established successfully
2025-06-07 15:27:47.176 +10:00 [INF] Database service initialized successfully
2025-06-07 15:27:47.177 +10:00 [INF] Initializing MYOB service...
2025-06-07 15:27:47.187 +10:00 [INF] Initializing MYOB service...
2025-06-07 15:27:47.190 +10:00 [INF] MYOB Configuration Debug:
2025-06-07 15:27:47.193 +10:00 [INF]   DeveloperKey: [SET - Length: 36]
2025-06-07 15:27:47.202 +10:00 [INF]   DeveloperSecret: [SET - Length: 24]
2025-06-07 15:27:47.205 +10:00 [INF]   ConfirmationUrl: 'http://desktop'
2025-06-07 15:27:47.206 +10:00 [INF]   CompanyFileName: 'LEP Colour Printers Pty Ltd'
2025-06-07 15:27:47.209 +10:00 [INF]   Username: [SET]
2025-06-07 15:27:47.211 +10:00 [INF]   Password: [SET]
2025-06-07 15:27:47.216 +10:00 [INF] Using username/password authentication
2025-06-07 15:27:47.432 +10:00 [ERR] Failed to initialize MYOB service
MYOB.AccountRight.SDK.ApiCommunicationException: Encountered a communication error (https://api.myob.com/accountright)
 ---> System.Net.WebException: The remote server returned an error: (401) Unauthorized.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiGetRequestSync[T](Uri uri, ICompanyFileCredentials credentials, String eTag)
   at MYOB.AccountRight.SDK.Services.CompanyFileService.GetRange()
   at LepInvoicer.Services.MYOBService.<>c__DisplayClass15_0.<InitializeAsync>b__1() in C:\LepSF\au\LepInvoicer\Services\MYOBService.cs:line 90
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Services.MYOBService.InitializeAsync() in C:\LepSF\au\LepInvoicer\Services\MYOBService.cs:line 90
2025-06-07 15:27:47.468 +10:00 [ERR] LEP Invoicer failed after 316ms
MYOB.AccountRight.SDK.ApiCommunicationException: Encountered a communication error (https://api.myob.com/accountright)
 ---> System.Net.WebException: The remote server returned an error: (401) Unauthorized.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiGetRequestSync[T](Uri uri, ICompanyFileCredentials credentials, String eTag)
   at MYOB.AccountRight.SDK.Services.CompanyFileService.GetRange()
   at LepInvoicer.Services.MYOBService.<>c__DisplayClass15_0.<InitializeAsync>b__1() in C:\LepSF\au\LepInvoicer\Services\MYOBService.cs:line 90
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Services.MYOBService.InitializeAsync() in C:\LepSF\au\LepInvoicer\Services\MYOBService.cs:line 90
   at LepInvoicer.Services.InvoicerService.InitializeServicesAsync() in C:\LepSF\au\LepInvoicer\Services\InvoicerService.cs:line 91
   at LepInvoicer.Services.InvoicerService.RunInvoicerAsync() in C:\LepSF\au\LepInvoicer\Services\InvoicerService.cs:line 49
2025-06-07 15:27:47.503 +10:00 [INF] Database connection disposed
2025-06-07 15:27:47.506 +10:00 [INF] LEP Invoicer completed with result: 1
2025-06-07 15:31:40.799 +10:00 [INF] Starting LEP Invoicer application
2025-06-07 15:31:40.896 +10:00 [INF] Creating host builder...
2025-06-07 15:31:40.905 +10:00 [INF] Building host...
2025-06-07 15:31:41.540 +10:00 [INF] Getting invoicer service...
2025-06-07 15:31:41.666 +10:00 [INF] MYOBService constructor - Configuration loaded. TestMode: false
2025-06-07 15:31:41.675 +10:00 [INF] MYOBService constructor - MYOB DeveloperKey: [SET]
2025-06-07 15:31:43.650 +10:00 [INF] Running invoicer...
2025-06-07 15:31:43.659 +10:00 [INF] Starting LEP Invoicer at "2025-06-07T15:31:43.6586829+10:00"
2025-06-07 15:31:43.670 +10:00 [INF] About to initialize services...
2025-06-07 15:31:43.677 +10:00 [INF] Initializing services...
2025-06-07 15:31:43.680 +10:00 [INF] Initializing database service...
2025-06-07 15:31:43.686 +10:00 [INF] Initializing database connection and NHibernate session...
2025-06-07 15:31:43.693 +10:00 [INF] Database connection established successfully
2025-06-07 15:31:43.695 +10:00 [INF] Database service initialized successfully
2025-06-07 15:31:43.706 +10:00 [INF] Initializing MYOB service...
2025-06-07 15:31:43.721 +10:00 [INF] Initializing MYOB service...
2025-06-07 15:31:43.724 +10:00 [INF] MYOB Configuration Debug:
2025-06-07 15:31:43.737 +10:00 [INF]   DeveloperKey: [SET - Length: 36]
2025-06-07 15:31:43.744 +10:00 [INF]   DeveloperSecret: [SET - Length: 24]
2025-06-07 15:31:43.746 +10:00 [INF]   ConfirmationUrl: 'http://desktop'
2025-06-07 15:31:43.753 +10:00 [INF]   CompanyFileName: 'LEP Colour Printers Pty Ltd'
2025-06-07 15:31:43.756 +10:00 [INF]   Username: [SET]
2025-06-07 15:31:43.762 +10:00 [INF]   Password: [SET]
2025-06-07 15:31:43.766 +10:00 [INF] Initializing MYOB with OAuth authentication
2025-06-07 15:31:43.771 +10:00 [INF] Starting OAuth authentication flow
2025-06-07 15:31:43.924 +10:00 [INF] Using existing OAuth tokens
2025-06-07 15:31:43.938 +10:00 [INF] Getting company files from MYOB
2025-06-07 15:31:44.282 +10:00 [INF] Found 1 company files
2025-06-07 15:31:44.285 +10:00 [INF] MYOB service initialized with OAuth credentials. Using company file: LEP Colour Printers Pty Ltd
2025-06-07 15:31:45.121 +10:00 [INF] Cached 279 accounts from MYOB
2025-06-07 15:31:45.462 +10:00 [INF] Cached GST tax code from MYOB
2025-06-07 15:31:45.614 +10:00 [INF] MYOB service initialized successfully with company file: LEP Colour Printers Pty Ltd
2025-06-07 15:31:45.617 +10:00 [INF] MYOB service initialized successfully
2025-06-07 15:31:45.619 +10:00 [INF] Setting up FastReport...
2025-06-07 15:31:45.644 +10:00 [INF] FastReport setup completed
2025-06-07 15:31:45.664 +10:00 [INF] All services initialized successfully
2025-06-07 15:31:45.666 +10:00 [INF] Services initialization completed
2025-06-07 15:31:45.672 +10:00 [INF] Processing order invoices...
2025-06-07 15:31:45.676 +10:00 [INF] Getting 20 orders to invoice
2025-06-07 15:31:46.592 +10:00 [INF] Found 0 orders to invoice
2025-06-07 15:31:46.594 +10:00 [INF] Found 0 orders to process
2025-06-07 15:31:46.597 +10:00 [INF] Order processing completed. Processed: 0, Success: 0, Failed: 0
2025-06-07 15:31:46.600 +10:00 [INF] Processing credit invoices...
2025-06-07 15:31:46.603 +10:00 [INF] Getting 10 credits to invoice
2025-06-07 15:31:46.625 +10:00 [INF] Found 0 credits to invoice
2025-06-07 15:31:46.632 +10:00 [INF] Processing refund invoices...
2025-06-07 15:31:46.643 +10:00 [INF] Getting 10 refunds to invoice
2025-06-07 15:31:46.651 +10:00 [INF] Found 0 refunds to invoice
2025-06-07 15:31:46.653 +10:00 [INF] Cleaning up invoicer logs...
2025-06-07 15:31:46.727 +10:00 [INF] LEP Invoicer completed successfully in 3068ms. Orders: 0, Credits: 0, Refunds: 0
2025-06-07 15:31:46.749 +10:00 [INF] Database connection disposed
2025-06-07 15:31:46.752 +10:00 [INF] LEP Invoicer completed with result: 0
