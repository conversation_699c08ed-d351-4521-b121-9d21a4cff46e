2025-06-07 18:48:47.117 +10:00 [INF] Starting LEP Invoicer application
2025-06-07 18:48:51.320 +10:00 [INF] Starting LEP Invoicer at "2025-06-07T18:48:51.3067128+10:00"
2025-06-07 18:48:51.349 +10:00 [INF] Initializing services...
2025-06-07 18:48:51.352 +10:00 [INF] Initializing database connection and NHibernate session...
2025-06-07 18:48:51.360 +10:00 [INF] Database connection established successfully
2025-06-07 18:48:51.427 +10:00 [INF] Initializing MYOB service...
2025-06-07 18:48:51.430 +10:00 [WRN] MYOB service running in TEST MODE - MYOB integration disabled
2025-06-07 18:48:51.519 +10:00 [INF] Services initialized successfully
2025-06-07 18:48:51.524 +10:00 [INF] Processing order invoices...
2025-06-07 18:48:51.530 +10:00 [INF] Getting 50 orders to invoice
2025-06-07 18:48:52.520 +10:00 [INF] Found 0 orders to invoice
2025-06-07 18:48:52.523 +10:00 [INF] Found 0 orders to process
2025-06-07 18:48:52.527 +10:00 [INF] Order processing completed. Processed: 0, Success: 0, Failed: 0
2025-06-07 18:48:52.536 +10:00 [INF] Processing credit invoices...
2025-06-07 18:48:52.540 +10:00 [INF] Getting 10 credits to invoice
2025-06-07 18:48:52.603 +10:00 [INF] Found 0 credits to invoice
2025-06-07 18:48:52.611 +10:00 [INF] Processing refund invoices...
2025-06-07 18:48:52.615 +10:00 [INF] Getting 10 refunds to invoice
2025-06-07 18:48:52.631 +10:00 [INF] Found 0 refunds to invoice
2025-06-07 18:48:52.633 +10:00 [INF] Cleaning up invoicer logs...
2025-06-07 18:48:52.704 +10:00 [INF] LEP Invoicer completed successfully in 1397ms. Orders: 0, Credits: 0, Refunds: 0
2025-06-07 18:48:52.723 +10:00 [INF] Database connection disposed
2025-06-07 18:48:52.726 +10:00 [INF] LEP Invoicer completed with result: 0
2025-06-07 18:50:50.621 +10:00 [INF] Starting LEP Invoicer application
2025-06-07 18:50:53.530 +10:00 [INF] Starting LEP Invoicer at "2025-06-07T18:50:53.5196822+10:00"
2025-06-07 18:50:53.542 +10:00 [INF] Initializing services...
2025-06-07 18:50:53.544 +10:00 [INF] Initializing database connection and NHibernate session...
2025-06-07 18:50:53.549 +10:00 [INF] Database connection established successfully
2025-06-07 18:50:53.556 +10:00 [INF] Initializing MYOB service...
2025-06-07 18:50:53.558 +10:00 [WRN] MYOB service running in TEST MODE - MYOB integration disabled
2025-06-07 18:50:53.674 +10:00 [INF] Test OAuth service created successfully
2025-06-07 18:50:53.689 +10:00 [INF] Services initialized successfully
2025-06-07 18:50:53.695 +10:00 [INF] Processing order invoices...
2025-06-07 18:50:53.702 +10:00 [INF] Getting 50 orders to invoice
2025-06-07 18:50:54.605 +10:00 [INF] Found 0 orders to invoice
2025-06-07 18:50:54.608 +10:00 [INF] Found 0 orders to process
2025-06-07 18:50:54.611 +10:00 [INF] Order processing completed. Processed: 0, Success: 0, Failed: 0
2025-06-07 18:50:54.616 +10:00 [INF] Processing credit invoices...
2025-06-07 18:50:54.619 +10:00 [INF] Getting 10 credits to invoice
2025-06-07 18:50:54.674 +10:00 [INF] Found 0 credits to invoice
2025-06-07 18:50:54.681 +10:00 [INF] Processing refund invoices...
2025-06-07 18:50:54.684 +10:00 [INF] Getting 10 refunds to invoice
2025-06-07 18:50:54.704 +10:00 [INF] Found 0 refunds to invoice
2025-06-07 18:50:54.707 +10:00 [INF] Cleaning up invoicer logs...
2025-06-07 18:50:54.778 +10:00 [INF] LEP Invoicer completed successfully in 1258ms. Orders: 0, Credits: 0, Refunds: 0
2025-06-07 18:50:54.785 +10:00 [INF] Database connection disposed
2025-06-07 18:50:54.788 +10:00 [INF] LEP Invoicer completed with result: 0
2025-06-07 18:58:59.355 +10:00 [INF] Starting LEP Invoicer application
2025-06-07 18:59:03.160 +10:00 [INF] Starting LEP Invoicer at "2025-06-07T18:59:03.1460188+10:00"
2025-06-07 18:59:03.239 +10:00 [INF] Initializing services...
2025-06-07 18:59:03.253 +10:00 [INF] Initializing database connection and NHibernate session...
2025-06-07 18:59:03.459 +10:00 [ERR] Failed to initialize database connection
Microsoft.Data.SqlClient.SqlException (0x80131904): A connection was successfully established with the server, but then an error occurred during the login process. (provider: SSL Provider, error: 0 - The certificate chain was issued by an authority that is not trusted.)
 ---> System.ComponentModel.Win32Exception (0x80090325): The certificate chain was issued by an authority that is not trusted.
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParserStateObject.ThrowExceptionAndWarning(Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParserStateObject.SNIWritePacket(PacketHandle packet, UInt32& sniError, Boolean canAccumulate, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParserStateObject.WriteSni(Boolean canAccumulate)
   at Microsoft.Data.SqlClient.TdsParserStateObject.WritePacket(Byte flushMode, Boolean canAccumulate)
   at Microsoft.Data.SqlClient.TdsParser.TdsLogin(SqlLogin rec, FeatureExtension requestedFeatures, SessionData recoverySessionData, FederatedAuthenticationFeatureExtensionData fedAuthFeatureExtensionData, SqlConnectionEncryptOption encrypt)
   at Microsoft.Data.SqlClient.SqlInternalConnectionTds.Login(ServerInfo server, TimeoutTimer timeout, String newPassword, SecureString newSecurePassword, SqlConnectionEncryptOption encrypt)
   at Microsoft.Data.SqlClient.SqlInternalConnectionTds.AttemptOneLogin(ServerInfo serverInfo, String newPassword, SecureString newSecurePassword, TimeoutTimer timeout, Boolean withFailover)
   at Microsoft.Data.SqlClient.SqlInternalConnectionTds.LoginNoFailover(ServerInfo serverInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString connectionOptions, SqlCredential credential, TimeoutTimer timeout)
   at Microsoft.Data.SqlClient.SqlInternalConnectionTds.OpenLoginEnlist(TimeoutTimer timeout, SqlConnectionString connectionOptions, SqlCredential credential, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance)
   at Microsoft.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken, DbConnectionPool pool, Func`3 accessTokenCallback)
   at Microsoft.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at Microsoft.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at Microsoft.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at Microsoft.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at Microsoft.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at Microsoft.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at Microsoft.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at Microsoft.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at Microsoft.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at Microsoft.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry, SqlConnectionOverrides overrides)
   at Microsoft.Data.SqlClient.SqlConnection.Open(SqlConnectionOverrides overrides)
   at Microsoft.Data.SqlClient.SqlConnection.Open()
   at LepInvoicer.Services.DatabaseService.Initialize() in C:\LepSF\au\LepInvoicer\Services\DatabaseService.cs:line 36
ClientConnectionId:a3f7ea31-d4c0-49e5-a7eb-6cb7e0c7502e
Error Number:-**********,State:0,Class:20
2025-06-07 18:59:03.516 +10:00 [ERR] LEP Invoicer failed after 370ms
Microsoft.Data.SqlClient.SqlException (0x80131904): A connection was successfully established with the server, but then an error occurred during the login process. (provider: SSL Provider, error: 0 - The certificate chain was issued by an authority that is not trusted.)
 ---> System.ComponentModel.Win32Exception (0x80090325): The certificate chain was issued by an authority that is not trusted.
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParserStateObject.ThrowExceptionAndWarning(Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParserStateObject.SNIWritePacket(PacketHandle packet, UInt32& sniError, Boolean canAccumulate, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParserStateObject.WriteSni(Boolean canAccumulate)
   at Microsoft.Data.SqlClient.TdsParserStateObject.WritePacket(Byte flushMode, Boolean canAccumulate)
   at Microsoft.Data.SqlClient.TdsParser.TdsLogin(SqlLogin rec, FeatureExtension requestedFeatures, SessionData recoverySessionData, FederatedAuthenticationFeatureExtensionData fedAuthFeatureExtensionData, SqlConnectionEncryptOption encrypt)
   at Microsoft.Data.SqlClient.SqlInternalConnectionTds.Login(ServerInfo server, TimeoutTimer timeout, String newPassword, SecureString newSecurePassword, SqlConnectionEncryptOption encrypt)
   at Microsoft.Data.SqlClient.SqlInternalConnectionTds.AttemptOneLogin(ServerInfo serverInfo, String newPassword, SecureString newSecurePassword, TimeoutTimer timeout, Boolean withFailover)
   at Microsoft.Data.SqlClient.SqlInternalConnectionTds.LoginNoFailover(ServerInfo serverInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString connectionOptions, SqlCredential credential, TimeoutTimer timeout)
   at Microsoft.Data.SqlClient.SqlInternalConnectionTds.OpenLoginEnlist(TimeoutTimer timeout, SqlConnectionString connectionOptions, SqlCredential credential, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance)
   at Microsoft.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken, DbConnectionPool pool, Func`3 accessTokenCallback)
   at Microsoft.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at Microsoft.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at Microsoft.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at Microsoft.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at Microsoft.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at Microsoft.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at Microsoft.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at Microsoft.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at Microsoft.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at Microsoft.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry, SqlConnectionOverrides overrides)
   at Microsoft.Data.SqlClient.SqlConnection.Open(SqlConnectionOverrides overrides)
   at Microsoft.Data.SqlClient.SqlConnection.Open()
   at LepInvoicer.Services.DatabaseService.Initialize() in C:\LepSF\au\LepInvoicer\Services\DatabaseService.cs:line 36
   at LepInvoicer.Services.InvoicerService.InitializeServices() in C:\LepSF\au\LepInvoicer\Services\InvoicerService.cs:line 82
   at LepInvoicer.Services.InvoicerService.RunInvoicer() in C:\LepSF\au\LepInvoicer\Services\InvoicerService.cs:line 46
ClientConnectionId:a3f7ea31-d4c0-49e5-a7eb-6cb7e0c7502e
Error Number:-**********,State:0,Class:20
2025-06-07 18:59:03.591 +10:00 [INF] Database connection disposed
2025-06-07 18:59:03.595 +10:00 [INF] LEP Invoicer completed with result: 1
