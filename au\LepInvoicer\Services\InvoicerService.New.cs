using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using LepInvoicer.Contracts.Services;

namespace LepInvoicer.Services;

/// <summary>
/// Main invoicer service implementation for orchestrating the invoicing process
/// </summary>
public class InvoicerService : IInvoicerService
{
    private readonly ILogger<InvoicerService> _logger;
    private readonly InvoicerConfiguration _config;
    private readonly IDatabaseService _databaseService;
    private readonly IMYOBService _myobService;

    public InvoicerService(
        ILogger<InvoicerService> logger,
        IOptions<InvoicerConfiguration> config,
        IDatabaseService databaseService,
        IMYOBService myobService)
    {
        _logger = logger;
        _config = config.Value;
        _databaseService = databaseService;
        _myobService = myobService;
    }

    public async Task<int> RunInvoicer()
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        
        try
        {
            _logger.LogInformation("Starting LEP Invoicer process...");

            // Initialize services
            await InitializeServices();

            // Process invoices
            var orderResult = await ProcessOrderInvoices();
            var creditResult = await ProcessCreditInvoices();
            var refundResult = await ProcessRefundInvoices();

            // Calculate totals
            var totalProcessed = orderResult.ProcessedCount + creditResult.ProcessedCount + refundResult.ProcessedCount;
            var totalSuccessful = orderResult.SuccessCount + creditResult.SuccessCount + refundResult.SuccessCount;
            var totalFailed = orderResult.FailureCount + creditResult.FailureCount + refundResult.FailureCount;

            stopwatch.Stop();

            _logger.LogInformation(
                "LEP Invoicer completed in {ElapsedTime:F1}s. " +
                "Processed: {TotalProcessed} (Orders: {Orders}, Credits: {Credits}, Refunds: {Refunds}). " +
                "Successful: {TotalSuccessful}, Failed: {TotalFailed}",
                stopwatch.Elapsed.TotalSeconds,
                totalProcessed,
                orderResult.ProcessedCount,
                creditResult.ProcessedCount,
                refundResult.ProcessedCount,
                totalSuccessful,
                totalFailed);

            return totalProcessed;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "LEP Invoicer failed with error");
            throw;
        }
    }

    private async Task InitializeServices()
    {
        _logger.LogInformation("Initializing services...");
        
        await _databaseService.Initialize();
        await _myobService.Initialize();
        
        _logger.LogInformation("Services initialized successfully");
    }

    private async Task<ProcessingResult> ProcessOrderInvoices()
    {
        _logger.LogInformation("Processing order invoices...");
        
        var result = new ProcessingResult();
        var orders = await _databaseService.GetOrdersToInvoice(_config.InvoiceBatchSize);

        foreach (var orderInfo in orders)
        {
            try
            {
                var order = await _databaseService.GetOrder(orderInfo.Key);
                var success = await _myobService.CreateOrderInvoice(order);
                
                if (success)
                {
                    await _databaseService.MarkOrderInvoiced(order.Id);
                    
                    // Log success with useful details
                    var details = CreateOrderDetails(order);
                    await _databaseService.LogInvoicingResult(
                        order.Id,
                        order.Jobs.Count,
                        order.PriceOfJobs ?? 0,
                        order.FinishDate ?? DateTime.Now,
                        true,
                        details);
                    
                    result.SuccessCount++;
                }
                else
                {
                    result.FailureCount++;
                }
                result.ProcessedCount++;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process order {OrderId}", orderInfo.Key);
                
                await _databaseService.MarkOrderFailed(orderInfo.Key, ex.Message);
                await _databaseService.LogInvoicingResult(
                    orderInfo.Key,
                    0,
                    0,
                    DateTime.Now,
                    false,
                    ex.Message);
                
                result.FailureCount++;
                result.ProcessedCount++;
            }
        }

        _logger.LogInformation("Order processing completed. Processed: {ProcessedCount}, Successful: {SuccessCount}, Failed: {FailureCount}",
            result.ProcessedCount, result.SuccessCount, result.FailureCount);

        return result;
    }

    private async Task<ProcessingResult> ProcessCreditInvoices()
    {
        _logger.LogInformation("Processing credit invoices...");
        
        var result = new ProcessingResult();
        var credits = await _databaseService.GetCreditsToInvoice(_config.CreditBatchSize);

        foreach (var credit in credits)
        {
            try
            {
                var success = await _myobService.CreateCreditInvoice(credit);
                if (success)
                {
                    await _databaseService.MarkCreditInvoiced(credit.Id);
                    
                    // Log success to Invoicer2Log (matching LINQPad script)
                    if (credit.Order != null)
                    {
                        await _databaseService.LogInvoicingResult(
                            credit.Order.Id,
                            1, // JobCount = 1 for credits
                            credit.Amount,
                            credit.DateCreated,
                            true,
                            $"Credit Type {credit.Type} - CreditId {credit.Id}");
                    }
                    
                    result.SuccessCount++;
                }
                else
                {
                    result.FailureCount++;
                }
                result.ProcessedCount++;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process credit {CreditId}", credit.Id);
                
                // Log failure to Invoicer2Log
                if (credit.Order != null)
                {
                    await _databaseService.LogInvoicingResult(
                        credit.Order.Id,
                        1,
                        credit.Amount,
                        credit.DateCreated,
                        false,
                        ex.Message);
                }
                
                result.FailureCount++;
                result.ProcessedCount++;
            }
        }

        _logger.LogInformation("Credit processing completed. Processed: {ProcessedCount}, Successful: {SuccessCount}, Failed: {FailureCount}",
            result.ProcessedCount, result.SuccessCount, result.FailureCount);

        return result;
    }

    private async Task<ProcessingResult> ProcessRefundInvoices()
    {
        _logger.LogInformation("Processing refund invoices...");
        
        var result = new ProcessingResult();
        var refunds = await _databaseService.GetRefundsToInvoice(_config.RefundBatchSize);

        foreach (var refund in refunds)
        {
            try
            {
                var success = await _myobService.CreateRefundInvoice(refund);
                if (success)
                {
                    await _databaseService.MarkCreditInvoiced(refund.Id);
                    
                    // Log success to Invoicer2Log with NULL OrderId
                    await _databaseService.LogInvoicingResult(
                        0, // Use 0 as placeholder, will be converted to NULL in database
                        1, // JobCount = 1 for refunds
                        refund.Amount,
                        refund.DateCreated,
                        true,
                        $"Refund Type {refund.Type} - CreditId {refund.Id} - CustomerId {refund.Customer?.Id}");
                    
                    result.SuccessCount++;
                }
                else
                {
                    result.FailureCount++;
                }
                result.ProcessedCount++;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process refund {RefundId}", refund.Id);
                
                // Log failure to Invoicer2Log
                await _databaseService.LogInvoicingResult(
                    0, // Use 0 as placeholder, will be converted to NULL in database
                    1,
                    refund.Amount,
                    refund.DateCreated,
                    false,
                    ex.Message);
                
                result.FailureCount++;
                result.ProcessedCount++;
            }
        }

        _logger.LogInformation("Refund processing completed. Processed: {ProcessedCount}, Successful: {SuccessCount}, Failed: {FailureCount}",
            result.ProcessedCount, result.SuccessCount, result.FailureCount);

        return result;
    }

    /// <summary>
    /// Create useful details string for order logging
    /// </summary>
    private string CreateOrderDetails(IOrder order)
    {
        var details = new List<string>();

        // Add customer information
        if (order.Customer != null)
        {
            details.Add($"Customer: {order.Customer.Name} ({order.Customer.Username})");
        }

        // Add invoice number
        details.Add($"Invoice: O{order.Id}");

        // Add purchase order if available
        if (!string.IsNullOrEmpty(order.PurchaseOrder))
        {
            details.Add($"PO: {order.PurchaseOrder}");
        }

        // Add job count
        details.Add($"Jobs: {order.Jobs?.Count ?? 0}");

        // Add finish date
        if (order.FinishDate.HasValue)
        {
            details.Add($"Finished: {order.FinishDate.Value:yyyy-MM-dd}");
        }

        return string.Join(" | ", details);
    }

    private class ProcessingResult
    {
        public int ProcessedCount { get; set; }
        public int SuccessCount { get; set; }
        public int FailureCount { get; set; }
    }
}
