# Simple query to check Details column for specific orders
$serverName = "SRV03"
$databaseName = "PRD_AU"
$username = "sa"
$password = "11_Fore5tGl5n"

Write-Host "=== DETAILS COLUMN ANALYSIS ===" -ForegroundColor Green

# Simple query for the specific orders
$query = @"
SELECT 
    OrderId,
    Success,
    CAST(Details AS VARCHAR(200)) AS Details,
    DateCreated
FROM Invoicer2Log 
WHERE OrderId IN (1416230,1416418,1416327,1415849,1416128,1415366)
ORDER BY OrderId
"@

Write-Host "Checking Details for your specific orders:" -ForegroundColor Yellow
sqlcmd -S $serverName -d $databaseName -U $username -P $password -Q $query

Write-Host ""
Write-Host "=== COMPARISON WITH RECENT ENTRIES ===" -ForegroundColor Green

# Compare with recent entries that have Details
$comparisonQuery = @"
SELECT TOP 10
    OrderId,
    Success,
    CAST(Details AS VARCHAR(200)) AS Details,
    DateCreated
FROM Invoicer2Log 
WHERE Details IS NOT NULL 
ORDER BY DateCreated DESC
"@

Write-Host "Recent entries WITH Details:" -ForegroundColor Yellow
sqlcmd -S $serverName -d $databaseName -U $username -P $password -Q $comparisonQuery

Write-Host ""
Write-Host "=== SUMMARY ===" -ForegroundColor Green

# Count entries with/without Details
$summaryQuery = @"
SELECT 
    'Total Entries' as Category,
    COUNT(*) as Count
FROM Invoicer2Log
UNION ALL
SELECT 
    'Entries with NULL Details' as Category,
    COUNT(*) as Count
FROM Invoicer2Log
WHERE Details IS NULL
UNION ALL
SELECT 
    'Entries with Details' as Category,
    COUNT(*) as Count
FROM Invoicer2Log
WHERE Details IS NOT NULL
"@

Write-Host "Summary of Details column:" -ForegroundColor Yellow
sqlcmd -S $serverName -d $databaseName -U $username -P $password -Q $summaryQuery
