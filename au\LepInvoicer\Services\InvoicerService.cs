using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using LepInvoicer.Configuration;
using LepInvoicer.Contracts.Services;
using System.Diagnostics;

namespace LepInvoicer.Services;

/// <summary>
/// Main invoicer service implementation with improved error handling and structure
/// </summary>
public class InvoicerService : IInvoicerService
{
    private readonly ILogger<InvoicerService> _logger;
    private readonly InvoicerConfiguration _config;
    private readonly IMYOBService _myobService;
    private readonly IEmailService _emailService;
    private readonly LepInvoicer.Contracts.Services.IPdfService _pdfService;
    private readonly IDatabaseService _databaseService;

    public InvoicerService(
        ILogger<InvoicerService> logger,
        IOptions<InvoicerConfiguration> config,
        IMYOBService myobService,
        IEmailService emailService,
        LepInvoicer.Contracts.Services.IPdfService pdfService,
        IDatabaseService databaseService)
    {
        _logger = logger;
        _config = config.Value;
        _myobService = myobService;
        _emailService = emailService;
        _pdfService = pdfService;
        _databaseService = databaseService;
    }

    public async Task<int> RunInvoicer()
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            _logger.LogInformation("Starting LEP Invoicer at {StartTime}", DateTime.Now);

            // Initialize services
            await InitializeServices();

            // Process orders
            var orderResults = await ProcessOrderInvoices();

            // Process credits
            var creditResults = await ProcessCreditInvoices();

            // Process refunds
            var refundResults = await ProcessRefundInvoices();

            // Cleanup
            await _databaseService.CleanupInvoicerLogs();

            stopwatch.Stop();
            
            _logger.LogInformation("LEP Invoicer completed successfully in {ElapsedTime}ms. Orders: {OrderCount}, Credits: {CreditCount}, Refunds: {RefundCount}",
                stopwatch.ElapsedMilliseconds, orderResults.ProcessedCount, creditResults.ProcessedCount, refundResults.ProcessedCount);

            return 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "LEP Invoicer failed after {ElapsedTime}ms", stopwatch.ElapsedMilliseconds);
            return 1;
        }
        finally
        {
            _databaseService?.Dispose();
        }
    }

    private async Task InitializeServices()
    {
        _logger.LogInformation("Initializing services...");

        await _databaseService.Initialize();
        await _myobService.Initialize();

        // Setup FastReport
        FastReport.Utils.Config.FontListFolder = _config.FontListFolder;

        _logger.LogInformation("Services initialized successfully");
    }

    private async Task<ProcessingResult> ProcessOrderInvoices()
    {
        // Early return if disabled
        if (!_config.CreateOrderInvoice)
        {
            _logger.LogInformation("Order invoice creation is disabled");
            return new ProcessingResult();
        }

        _logger.LogInformation("Processing order invoices...");

        var orders = await _databaseService.GetOrdersToInvoice(_config.InvoiceBatchSize);
        _logger.LogInformation("Found {OrderCount} orders to process", orders.Count);

        var result = new ProcessingResult();

        foreach (var orderInfo in orders)
        {
            try
            {
                var order = await _databaseService.GetOrder(orderInfo.Key);

                var success = await ProcessSingleOrder(order);
                if (success)
                {
                    result.SuccessCount++;
                    await _databaseService.MarkOrderInvoiced(orderInfo.Key); // Use orderInfo.Key instead of order.Id
                }
                else
                {
                    result.FailureCount++;
                }

                result.ProcessedCount++;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process order {OrderId}", orderInfo.Key);
                result.FailureCount++;
                result.ProcessedCount++;

                await _databaseService.MarkOrderFailed(orderInfo.Key, ex.Message);
            }
        }

        _logger.LogInformation("Order processing completed. Processed: {ProcessedCount}, Success: {SuccessCount}, Failed: {FailureCount}",
            result.ProcessedCount, result.SuccessCount, result.FailureCount);

        return result;
    }

    private async Task<bool> ProcessSingleOrder(IOrder order)
    {
        _logger.LogInformation("Processing order {OrderId} with {JobCount} jobs, total: {Total:C}",
            order.Id, order.Jobs.Count, order.PriceOfJobs);

        try
        {
            // Early return if validation fails
            if (!ValidateOrder(order))
                return false;

            // Early return if MYOB invoice creation fails
            var myobSuccess = await _myobService.CreateOrderInvoice(order);
            if (!myobSuccess)
            {
                _logger.LogWarning("Failed to create MYOB invoice for order {OrderId}", order.Id);
                return false;
            }

            // Generate PDF if enabled
            if (_config.CreatePdfInvoice)
            {
                await GenerateAndProcessPdf(order);
            }

            // Log success with useful details
            var details = CreateOrderDetails(order);
            await _databaseService.LogInvoicingResult(
                order.Id,
                order.Jobs.Count,
                order.PriceOfJobs ?? 0,
                order.FinishDate ?? DateTime.Now,
                true,
                details);

            _logger.LogInformation("Successfully processed order {OrderId}", order.Id);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing order {OrderId}", order.Id);

            await _databaseService.LogInvoicingResult(
                order.Id,
                order.Jobs.Count,
                order.PriceOfJobs ?? 0,
                order.FinishDate ?? DateTime.Now,
                false,
                ex.Message);

            return false;
        }
    }

    private bool ValidateOrder(IOrder order)
    {
        // Early return for zero price
        if (order.PriceOfJobs == null || order.PriceOfJobs == 0)
        {
            _logger.LogWarning("Order {OrderId} has zero price, marking as invoiced", order.Id);
            return false;
        }

        // Early return for missing finish date
        if (order.FinishDate == null)
        {
            _logger.LogWarning("Order {OrderId} has no finish date", order.Id);
            return false;
        }

        return true;
    }

    /// <summary>
    /// Create useful details string for order logging
    /// </summary>
    private string CreateOrderDetails(IOrder order)
    {
        var details = new List<string>();

        // Add customer information
        if (order.Customer != null)
        {
            details.Add($"Customer: {order.Customer.Name} ({order.Customer.Username})");
        }

        // Add invoice number
        details.Add($"Invoice: O{order.Id}");

        // Add purchase order if available
        if (!string.IsNullOrEmpty(order.PurchaseOrder))
        {
            details.Add($"PO: {order.PurchaseOrder}");
        }

        // Add job count
        details.Add($"Jobs: {order.Jobs?.Count ?? 0}");

        // Add finish date
        if (order.FinishDate.HasValue)
        {
            details.Add($"Finished: {order.FinishDate.Value:yyyy-MM-dd}");
        }

        return string.Join(" | ", details);
    }

    private async Task GenerateAndProcessPdf(IOrder order)
    {
        try
        {
            var finishDate = order.FinishDate?.Date.ToString("yyyy/MMM/dd").Replace(".", "") ?? DateTime.Now.ToString("yyyy/MMM/dd");
            var pdfDirectory = Path.Combine(_config.PdfFolder, finishDate);
            Directory.CreateDirectory(pdfDirectory);

            var pdfPath = Path.Combine(pdfDirectory, $"O{order.Id}.pdf");

            await _pdfService.GenerateOrderInvoicePdf(order, pdfPath);

            // Copy to order folder
            await CopyPdfToOrderFolder(order, pdfPath);

            // Send email if enabled
            if (_config.EmailPdfInvoice)
            {
                await SendInvoiceEmail(order, pdfPath);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate/process PDF for order {OrderId}", order.Id);
            // Don't fail the entire order for PDF issues
        }
    }

    private Task CopyPdfToOrderFolder(IOrder order, string pdfPath)
    {
        try
        {
            var orderFolder = Path.Combine(_config.DataDirectoryFullName, "orders",
                order.DateCreated.ToString("yyyyMMdd"), order.OrderNr, "Extrafiles");

            Directory.CreateDirectory(orderFolder);

            var destinationPath = Path.Combine(orderFolder, $"O{order.Id}.pdf");
            File.Copy(pdfPath, destinationPath, true);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to copy PDF to order folder for order {OrderId}", order.Id);
        }

        return Task.CompletedTask;
    }

    private async Task SendInvoiceEmail(IOrder order, string pdfPath)
    {
        try
        {
            var emailAddress = _config.EmailPdfAddress ?? order.Customer.AccountEmail ?? order.Customer.Email;

            // Early return if no email address
            if (string.IsNullOrEmpty(emailAddress))
            {
                _logger.LogWarning("No email address found for order {OrderId}", order.Id);
                return;
            }

            var success = await _emailService.SendInvoiceEmail(emailAddress, $"O{order.Id}", pdfPath);

            // Log result based on success
            if (success)
            {
                _logger.LogInformation("Email sent successfully for order {OrderId} to {Email}", order.Id, emailAddress);
            }
            else
            {
                _logger.LogWarning("Failed to send email for order {OrderId} to {Email}", order.Id, emailAddress);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending email for order {OrderId}", order.Id);
        }
    }

    private async Task<ProcessingResult> ProcessCreditInvoices()
    {
        // Early return if disabled
        if (!_config.CreateRefundInvoice)
            return new ProcessingResult();

        _logger.LogInformation("Processing credit invoices...");

        var credits = await _databaseService.GetCreditsToInvoice(_config.RefundBatchSize);
        var result = new ProcessingResult();

        foreach (var credit in credits)
        {
            try
            {
                var success = await _myobService.CreateCreditInvoice(credit);
                if (success)
                {
                    await _databaseService.MarkCreditInvoiced(credit.Id);

                    // Log success to Invoicer2Log (matching LINQPad script)
                    if (credit.Order != null)
                    {
                        await _databaseService.LogInvoicingResult(
                            credit.Order.Id,
                            1, // JobCount = 1 for credits
                            credit.Amount,
                            credit.DateCreated,
                            true,
                            $"Credit Type {credit.Type} - CreditId {credit.Id}");
                    }

                    result.SuccessCount++;
                }
                else
                {
                    result.FailureCount++;
                }
                result.ProcessedCount++;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process credit {CreditId}", credit.Id);

                // Log failure to Invoicer2Log
                if (credit.Order != null)
                {
                    await _databaseService.LogInvoicingResult(
                        credit.Order.Id,
                        1,
                        credit.Amount,
                        credit.DateCreated,
                        false,
                        ex.Message);
                }

                result.FailureCount++;
                result.ProcessedCount++;
            }
        }

        return result;
    }

    private async Task<ProcessingResult> ProcessRefundInvoices()
    {
        // Early return if disabled
        if (!_config.CreateRefundInvoice)
            return new ProcessingResult();

        _logger.LogInformation("Processing refund invoices...");

        var refunds = await _databaseService.GetRefundsToInvoice(_config.RefundBatchSize);
        var result = new ProcessingResult();

        foreach (var refund in refunds)
        {
            try
            {
                var success = await _myobService.CreateRefundInvoice(refund);
                if (success)
                {
                    await _databaseService.MarkCreditInvoiced(refund.Id);

                    // Log success to Invoicer2Log with NULL OrderId (handled by database layer)
                    await _databaseService.LogInvoicingResult(
                        0, // Will be converted to NULL in database for refunds
                        1, // JobCount = 1 for refunds
                        refund.Amount,
                        refund.DateCreated,
                        true,
                        $"Refund Type {refund.Type} - CreditId {refund.Id} - CustomerId {refund.Customer?.Id}");

                    result.SuccessCount++;
                }
                else
                {
                    result.FailureCount++;
                }
                result.ProcessedCount++;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process refund {RefundId}", refund.Id);
                result.FailureCount++;
                result.ProcessedCount++;
            }
        }

        return result;
    }
}

/// <summary>
/// Result of processing a batch of items
/// </summary>
public class ProcessingResult
{
    public int ProcessedCount { get; set; }
    public int SuccessCount { get; set; }
    public int FailureCount { get; set; }
}
