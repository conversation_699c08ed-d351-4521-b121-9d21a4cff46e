using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Data.SqlClient;
using NHibernate;
using NHibernate.Linq;

namespace LepInvoicer.Services;

/// <summary>
/// Database service implementation
/// </summary>
public class DatabaseService : IDatabaseService, IDisposable
{
    private readonly ILogger<DatabaseService> _logger;
    private readonly InvoicerConfiguration _config;
    private SqlConnection? _connection;
    private ISession? _session;
    private bool _disposed = false;

    public DatabaseService(ILogger<DatabaseService> logger, IOptions<InvoicerConfiguration> config, ISession session)
    {
        _logger = logger;
        _config = config.Value;
        _session = session;
    }

    public Task InitializeAsync()
    {
        try
        {
            _logger.LogInformation("Initializing database connection and NHibernate session...");

            // Initialize SQL connection
            _connection = new SqlConnection(_config.ConnectionString);
            _connection.Open();

            // Execute initial cleanup SQL
            ExecuteSqlAsync(InvoicerConstants.SqlQueries.CleanupInitialSql).Wait();

            _logger.LogInformation("Database connection established successfully");
            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize database connection");
            throw;
        }
    }

    public Task<List<KeyValuePair<int, string>>> GetOrdersToInvoiceAsync(int batchSize)
    {
        if (_session == null)
            throw new InvalidOperationException("Database not initialized");

        _logger.LogInformation("Getting {BatchSize} orders to invoice", batchSize);

        try
        {
            var orders = _session.Query<IOrder>()
                .Where(o => !_config.IgnoreCustomers.Contains(o.Customer.Name))
                .Where(o => o.Invoiced2 == null || (o.Invoiced2 != "Y" && o.Invoiced2 != "F" && o.Invoiced2 != "C"))
                .Where(o => o.FinishDate != null && o.FinishDate.Value.Year != 1)
                .Where(o => o.FinishDate.Value.Date >= _config.MinimumFinishDate)
                .OrderBy(o => o.Id)
                .Select(o => new KeyValuePair<int, string>(o.Id, o.Customer.Username))
                .Take(batchSize)
                .ToList();

            _logger.LogInformation("Found {OrderCount} orders to invoice", orders.Count);
            return Task.FromResult(orders);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get orders to invoice");
            throw;
        }
    }

    public Task<List<OrderCredit>> GetCreditsToInvoiceAsync(int batchSize)
    {
        if (_session == null)
            throw new InvalidOperationException("Database not initialized");

        _logger.LogInformation("Getting {BatchSize} credits to invoice", batchSize);

        try
        {
            // Match LINQPad script: Type == "C" || Type == "M" || Type == "CI"
            var credits = _session.Query<OrderCredit>()
                .Fetch(c => c.Order)
                .Where(c => !c.Invoiced)
                .Where(c => c.Type == InvoicerConstants.CreditTypes.Credit ||
                           c.Type == InvoicerConstants.CreditTypes.Miscellaneous ||
                           c.Type == InvoicerConstants.CreditTypes.CreditInvoice)
                .Where(c => c.Order != null) // Must have associated order
                .Where(c => !_config.IgnoreCustomers.Contains(c.Order.Customer.Name))
                .OrderBy(c => c.DateCreated)
                .Take(batchSize)
                .ToList();

            _logger.LogInformation("Found {CreditCount} credits to invoice", credits.Count);
            return Task.FromResult(credits);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get credits to invoice");
            throw;
        }
    }

    public Task<List<OrderCredit>> GetRefundsToInvoiceAsync(int batchSize)
    {
        if (_session == null)
            throw new InvalidOperationException("Database not initialized");

        _logger.LogInformation("Getting {BatchSize} refunds to invoice", batchSize);

        try
        {
            // Match LINQPad script: Type == "S"
            var refunds = _session.Query<OrderCredit>()
                .Fetch(r => r.Customer)
                .Where(r => !r.Invoiced)
                .Where(r => r.Type == InvoicerConstants.CreditTypes.Refund) // Type == "S"
                .Where(r => r.Customer != null) // Must have customer
                .Where(r => !_config.IgnoreCustomers.Contains(r.Customer.Name))
                .OrderBy(r => r.DateCreated)
                .Take(batchSize)
                .ToList();

            _logger.LogInformation("Found {RefundCount} refunds to invoice", refunds.Count);
            return Task.FromResult(refunds);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get refunds to invoice");
            throw;
        }
    }

    public Task<IOrder> GetOrderAsync(int orderId)
    {
        if (_session == null)
            throw new InvalidOperationException("Database not initialized");

        _logger.LogInformation("Getting order {OrderId}", orderId);

        try
        {
            var order = _session.Query<IOrder>()
                .Fetch(x => x.Jobs)
                .Where(x => x.Id == orderId)
                .FirstOrDefault();

            if (order == null)
                throw new InvalidOperationException($"Order {orderId} not found");

            return Task.FromResult(order);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get order {OrderId}", orderId);
            throw;
        }
    }

    public Task ExecuteSqlAsync(string sql)
    {
        if (_connection == null)
            throw new InvalidOperationException("Database not initialized");

        try
        {
            _logger.LogDebug("Executing SQL: {Sql}", sql);
            
            using var command = new SqlCommand(sql, _connection);
            command.ExecuteNonQuery();
            
            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to execute SQL: {Sql}", sql);
            throw;
        }
    }

    public Task MarkOrderAsInvoicedAsync(int orderId)
    {
        var sql = string.Format(InvoicerConstants.SqlQueries.MarkOrderInvoicedSql, orderId);
        return ExecuteSqlAsync(sql);
    }

    public Task MarkOrderAsFailedAsync(int orderId, string errorMessage)
    {
        var escapedMessage = InvoicerUtilities.EscapeSqlString(errorMessage);
        var sql = string.Format(InvoicerConstants.SqlQueries.MarkOrderFailedSql, orderId, escapedMessage);
        return ExecuteSqlAsync(sql);
    }

    public Task MarkCreditAsInvoicedAsync(int creditId)
    {
        var sql = string.Format(InvoicerConstants.SqlQueries.MarkCreditInvoicedSql, creditId);
        return ExecuteSqlAsync(sql);
    }

    public Task LogInvoicingResultAsync(int orderId, int jobCount, decimal total, DateTime finishDate, bool success, string? details)
    {
        var successFlag = success ? InvoicerConstants.Database.LogSuccessFlag : InvoicerConstants.Database.LogFailureFlag;
        var escapedDetails = details != null ? $"'{InvoicerUtilities.EscapeSqlString(details)}'" : "null";
        
        var sql = string.Format(InvoicerConstants.SqlQueries.LogInvoicingResultSql,
            orderId, jobCount, total, finishDate.ToString(_config.DateFormat), successFlag, escapedDetails, DateTime.Now.ToString(_config.DateFormat));
        
        return ExecuteSqlAsync(sql);
    }

    public Task CleanupInvoicerLogsAsync()
    {
        _logger.LogInformation("Cleaning up invoicer logs...");
        return ExecuteSqlAsync(InvoicerConstants.SqlQueries.CleanupInvoicerLogsSql);
    }

    /// <summary>
    /// Get summary of OrderCredit types for debugging
    /// </summary>
    public Task<Dictionary<string, int>> GetOrderCreditTypeSummaryAsync()
    {
        if (_session == null)
            throw new InvalidOperationException("Database not initialized");

        try
        {
            var summary = _session.Query<OrderCredit>()
                .Where(c => !c.Invoiced)
                .GroupBy(c => c.Type)
                .Select(g => new { Type = g.Key, Count = g.Count() })
                .ToDictionary(x => x.Type ?? "NULL", x => x.Count);

            _logger.LogInformation("OrderCredit Type Summary: {Summary}",
                string.Join(", ", summary.Select(kvp => $"{kvp.Key}: {kvp.Value}")));

            return Task.FromResult(summary);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get OrderCredit type summary");
            throw;
        }
    }

    /// <summary>
    /// Create Invoicer2Log entries for 10 credits of each type
    /// </summary>
    public async Task CreateInvoicer2LogEntriesForCreditsAsync()
    {
        if (_connection == null)
            throw new InvalidOperationException("Database not initialized");

        try
        {
            _logger.LogInformation("=== CREATING INVOICER2LOG ENTRIES FOR CREDITS ===");

            // Get 10 records of each type
            var types = new[] { "C", "M", "CI", "S" };

            foreach (var type in types)
            {
                _logger.LogInformation("--- Processing Type '{Type}' ---", type);

                var sql = $@"
                    SELECT TOP 10
                        oc.Id,
                        oc.Type,
                        ISNULL(oc.Amount, 0) as Amount,
                        oc.DateCreated,
                        oc.OrderId,
                        oc.CustomerId
                    FROM OrderCredit oc
                    WHERE oc.Type = '{type}'
                    ORDER BY oc.DateCreated DESC";

                using var command = new SqlCommand(sql, _connection);
                using var reader = await command.ExecuteReaderAsync();

                var records = new List<(int Id, string Type, decimal Amount, DateTime DateCreated, int? OrderId, int? CustomerId)>();

                while (await reader.ReadAsync())
                {
                    var creditId = reader.GetInt32(0); // Id
                    var amount = reader.GetDecimal(2); // Amount
                    var dateCreated = reader.GetDateTime(3); // DateCreated
                    var orderIdFromCredit = reader.IsDBNull(4) ? (int?)null : reader.GetInt32(4); // OrderId
                    var customerId = reader.IsDBNull(5) ? (int?)null : reader.GetInt32(5); // CustomerId

                    records.Add((creditId, type, amount, dateCreated, orderIdFromCredit, customerId));
                }

                reader.Close();

                // Now create Invoicer2Log entries
                foreach (var record in records)
                {
                    await CreateInvoicer2LogEntryForCreditAsync(record.Id, record.Type, record.Amount, record.DateCreated, record.OrderId, record.CustomerId);
                }

                _logger.LogInformation("  Created {Count} Invoicer2Log entries for type '{Type}'", records.Count, type);
            }

            _logger.LogInformation("=== INVOICER2LOG CREATION COMPLETE ===");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create Invoicer2Log entries for credits");
            throw;
        }
    }

    /// <summary>
    /// Create a single Invoicer2Log entry for a credit
    /// </summary>
    private async Task CreateInvoicer2LogEntryForCreditAsync(int creditId, string type, decimal amount, DateTime dateCreated, int? orderId, int? customerId)
    {
        try
        {
            // Determine the OrderId to use for logging
            int logOrderId;

            if (orderId.HasValue)
            {
                // Use the associated order ID (for types C, M, CI)
                logOrderId = orderId.Value;
            }
            else if (customerId.HasValue)
            {
                // For refunds (type S) without OrderId, use CustomerId as OrderId
                logOrderId = customerId.Value;
            }
            else
            {
                // Fallback: use the credit ID itself
                logOrderId = creditId;
            }

            var sql = $@"
                INSERT INTO Invoicer2Log ([OrderId], [JobCount], [Total], [FinishDate], [Success], [Details], [DateCreated])
                VALUES ({logOrderId}, 1, {amount}, '{dateCreated:yyyy-MM-dd HH:mm:ss}', 'Y', 'Credit Type {type} - CreditId {creditId}', '{DateTime.Now:yyyy-MM-dd HH:mm:ss}')";

            using var command = new SqlCommand(sql, _connection);
            await command.ExecuteNonQueryAsync();

            _logger.LogInformation("  Created Invoicer2Log entry: CreditId={CreditId}, Type={Type}, Amount={Amount:C}, OrderId={OrderId}",
                creditId, type, amount, logOrderId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create Invoicer2Log entry for credit {CreditId}", creditId);
        }
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _connection?.Close();
            _connection?.Dispose();
            _disposed = true;
            _logger.LogInformation("Database connection disposed");
        }
    }
}
