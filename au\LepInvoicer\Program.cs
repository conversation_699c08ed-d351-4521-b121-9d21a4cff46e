using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Serilog;
using LepInvoicer.Contracts.Services;
using LepInvoicer.Services;

namespace LepInvoicer;

public class Program
{
	public static async Task<int> Main(string[] args)
	{
		// Setup logging first
		Log.Logger = new LoggerConfiguration()
			.WriteTo.Console()
			.WriteTo.File("logs/invoicer-.txt", rollingInterval: RollingInterval.Day)
			.CreateLogger();

		try
		{
			Log.Information("Starting LEP Invoicer application");

			var host = CreateHostBuilder(args).Build();
			var invoicer = host.Services.GetRequiredService<IInvoicerService>();

			var result = await invoicer.RunInvoicer();

			Log.Information("LEP Invoicer completed with result: {Result}", result);
			return result;
		}
		catch (Exception ex)
		{
			Log.Fatal(ex, "Application terminated unexpectedly");
			return 1;
		}
		finally
		{
			Log.CloseAndFlush();
		}
	}

	private static IHostBuilder CreateHostBuilder(string[] args) =>
		Host.CreateDefaultBuilder(args)
			.UseSerilog()
			.ConfigureServices((context, services) =>
			{
				services.Configure<InvoicerConfiguration>(context.Configuration.GetSection("Invoicer"));

				// Register NHibernate session factory and session
				services.AddSingleton<NHibernate.ISessionFactory>(provider =>
				{
					var config = provider.GetRequiredService<IOptions<InvoicerConfiguration>>().Value;

					var nhconfig = new NHibernate.Cfg.Configuration();
					nhconfig.SetProperty(NHibernate.Cfg.Environment.Dialect, typeof(NHibernate.Dialect.MsSql2008Dialect).AssemblyQualifiedName);
					nhconfig.SetProperty(NHibernate.Cfg.Environment.ConnectionDriver, typeof(NHibernate.Driver.SqlClientDriver).AssemblyQualifiedName);
					nhconfig.SetProperty(NHibernate.Cfg.Environment.Isolation, "ReadCommitted");
					nhconfig.SetProperty(NHibernate.Cfg.Environment.UseSecondLevelCache, false.ToString());
					nhconfig.SetProperty(NHibernate.Cfg.Environment.ShowSql, false.ToString());
					nhconfig.SetProperty(NHibernate.Cfg.Environment.ConnectionProvider, typeof(NHibernate.Connection.DriverConnectionProvider).AssemblyQualifiedName);
					nhconfig.SetProperty(NHibernate.Cfg.Environment.ConnectionString, config.ConnectionString);
					nhconfig.SetProperty(NHibernate.Cfg.Environment.CurrentSessionContextClass, "www");
					nhconfig.SetProperty(NHibernate.Cfg.Environment.BatchSize, "1000");

					// Add the assembly containing the domain entities
					nhconfig.AddAssembly(typeof(lep.user.ICustomerUser).Assembly);

					return nhconfig.BuildSessionFactory();
				});

				services.AddScoped<NHibernate.ISession>(provider =>
				{
					var sessionFactory = provider.GetRequiredService<NHibernate.ISessionFactory>();
					return sessionFactory.OpenSession();
				});

				// Configure AutoMapper
				var lepProfile = new LepCore.Setup.LepAutoMapperProfile(() => null);
				var mapperConfig = new MapperConfiguration(cfg => { cfg.AddProfile(lepProfile); });
				services.AddSingleton(mapperConfig.CreateMapper());

				// Register services
				services.AddScoped<IInvoicerService, InvoicerService>();
				services.AddScoped<IMYOBService, MYOBService>();
				services.AddScoped<IEmailService, EmailService>();
				services.AddScoped<LepInvoicer.Contracts.Services.IPdfService, PdfService>();
				services.AddScoped<IDatabaseService, DatabaseService>();
				services.AddScoped<LepInvoicer.Contracts.Services.IOAuthKeyService, OAuthKeyService>();
			});
}
