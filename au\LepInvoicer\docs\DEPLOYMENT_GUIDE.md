# LEP Invoicer Deployment Guide

## 🚀 Overview

This guide provides step-by-step instructions for deploying the LEP Invoicer application in production environments.

**Status**: ✅ **PRODUCTION READY** - Application is fully tested and ready for deployment.

## 📋 Prerequisites

### System Requirements
- **Operating System**: Windows Server 2019+ or Windows 10+
- **.NET Runtime**: .NET 8.0 Runtime (or SDK for development)
- **Database**: SQL Server with LEP database access
- **MYOB**: MYOB AccountRight cloud subscription with API access
- **Network**: Internet connectivity for MYOB cloud API

### Access Requirements
- **Database Access**: SQL Server connection with read/write permissions to LEP database
- **MYOB Access**: OAuth application credentials for MYOB AccountRight
- **Email Access**: SMTP server credentials (if email functionality enabled)
- **File System**: Write permissions for logs and OAuth token storage

## 🔧 Installation Steps

### 1. Download and Extract Application
```bash
# Extract the application to your desired location
# Example: C:\Applications\LepInvoicer\
```

### 2. Install .NET 8.0 Runtime
```bash
# Download from: https://dotnet.microsoft.com/download/dotnet/8.0
# Install the runtime (not SDK unless developing)
```

### 3. Configure Application Settings
Edit `appsettings.json` with your environment-specific settings:

```json
{
  "Serilog": {
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Warning",
        "System": "Warning"
      }
    },
    "WriteTo": [
      {
        "Name": "Console"
      },
      {
        "Name": "File",
        "Args": {
          "path": "logs/invoicer-.txt",
          "rollingInterval": "Day",
          "retainedFileCountLimit": 30,
          "outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}"
        }
      }
    ]
  },
  "InvoicerConfiguration": {
    "ConnectionString": "Data Source=YOUR_SERVER;user id=YOUR_USER;password=YOUR_PASSWORD;Initial Catalog=YOUR_DATABASE;TrustServerCertificate=true",
    "TestMode": false,
    "CreateOrderInvoice": true,
    "CreateRefundInvoice": true,
    "CreatePdfInvoice": true,
    "EmailPdfInvoice": true,
    "InvoiceBatchSize": 50,
    "RefundBatchSize": 10,
    "MinimumFinishDate": "2024-02-01",
    "IgnoreCustomers": [
      "LEP Colour Printers Pty Ltd",
      "LEP Marketing",
      "LEP TEST J",
      "LEP TEST T",
      "lepdemo"
    ],
    "MYOBConfiguration": {
      "ApiBaseUrl": "https://api.myob.com/accountright/",
      "ClientId": "YOUR_MYOB_CLIENT_ID",
      "ClientSecret": "YOUR_MYOB_CLIENT_SECRET",
      "RedirectUri": "http://localhost:8080/oauth/callback",
      "Scope": "CompanyFile"
    },
    "EmailConfiguration": {
      "SmtpServer": "YOUR_SMTP_SERVER",
      "SmtpPort": 587,
      "SmtpUsername": "YOUR_SMTP_USERNAME",
      "SmtpPassword": "YOUR_SMTP_PASSWORD",
      "FromEmail": "<EMAIL>",
      "FromName": "LEP Colour Printers"
    },
    "PdfConfiguration": {
      "TemplateFile": "C:\\LepData\\Labels2\\lep-invoice-order.frx",
      "OutputFolder": "C:\\LepData\\Invoices",
      "DataDirectory": "C:\\LepData"
    }
  }
}
```

### 4. Database Configuration
Ensure your SQL Server connection string includes:
- **TrustServerCertificate=true**: For internal servers with self-signed certificates
- **Proper credentials**: User with read/write access to LEP database
- **Network connectivity**: Server accessible from application host

### 5. MYOB OAuth Setup
1. **Register Application**: Register your application in MYOB Developer Portal
2. **Get Credentials**: Obtain ClientId and ClientSecret
3. **Configure Redirect**: Set redirect URI to match your configuration
4. **Initial Authentication**: Run application once to perform OAuth flow

### 6. File System Permissions
Ensure the application has:
- **Write access** to application directory (for Tokens.json)
- **Write access** to logs directory
- **Read access** to PDF template files
- **Write access** to PDF output directories

## 🔐 Security Configuration

### Database Security
- Use dedicated service account with minimal required permissions
- Enable SSL/TLS for database connections
- Regularly rotate database passwords

### MYOB Security
- Store OAuth credentials securely
- Monitor OAuth token usage
- Implement token rotation policies

### File System Security
- Restrict access to Tokens.json file
- Secure log file locations
- Implement log rotation and cleanup

## 🏃‍♂️ Running the Application

### Manual Execution
```bash
cd C:\Applications\LepInvoicer
dotnet LepInvoicer.dll
```

### Windows Service (Recommended)
1. **Install as Service**: Use tools like NSSM or create Windows Service wrapper
2. **Configure Service**: Set startup type to Automatic
3. **Service Account**: Use dedicated service account with required permissions
4. **Monitoring**: Implement service monitoring and alerting

### Scheduled Task
1. **Create Task**: Use Windows Task Scheduler
2. **Schedule**: Set appropriate frequency (e.g., every 30 minutes)
3. **Credentials**: Run with service account
4. **Logging**: Ensure task execution is logged

## 📊 Monitoring and Maintenance

### Log Monitoring
- **Location**: `logs/invoicer-{date}.txt`
- **Retention**: 30 days (configurable)
- **Monitoring**: Monitor for ERROR and WARNING levels
- **Alerts**: Set up alerts for critical errors

### Database Monitoring
- **Invoicer2Log Table**: Monitor processing results
- **Performance**: Monitor query performance and database locks
- **Cleanup**: Regular cleanup of old log entries

### MYOB API Monitoring
- **Rate Limits**: Monitor API call frequency
- **Authentication**: Monitor OAuth token renewal
- **Errors**: Track API error rates and types

### Performance Monitoring
- **Processing Times**: Monitor batch processing duration
- **Memory Usage**: Monitor application memory consumption
- **Disk Space**: Monitor log file growth and disk usage

## 🔧 Troubleshooting

### Common Issues

#### Database Connection Errors
```
Error: SSL Provider, error: 0 - The certificate chain was issued by an authority that is not trusted
Solution: Add TrustServerCertificate=true to connection string
```

#### MYOB Authentication Errors
```
Error: OAuth token expired or invalid
Solution: Delete Tokens.json and re-authenticate
```

#### File Permission Errors
```
Error: Access denied writing to Tokens.json
Solution: Grant write permissions to application directory
```

### Diagnostic Steps
1. **Check Logs**: Review application logs for detailed error information
2. **Test Connectivity**: Verify database and MYOB API connectivity
3. **Validate Configuration**: Ensure all configuration values are correct
4. **Check Permissions**: Verify file system and database permissions

## 📈 Performance Optimization

### Batch Size Tuning
- **InvoiceBatchSize**: Adjust based on processing time and system load
- **RefundBatchSize**: Smaller batches for better responsiveness
- **Monitoring**: Monitor processing times and adjust accordingly

### Database Optimization
- **Indexing**: Ensure proper indexes on query columns
- **Statistics**: Keep database statistics updated
- **Maintenance**: Regular database maintenance and cleanup

### MYOB API Optimization
- **Rate Limiting**: Respect API rate limits with built-in delays
- **Caching**: Leverage cached account and tax code data
- **Error Handling**: Implement proper retry logic for transient errors

## 🔄 Backup and Recovery

### Configuration Backup
- **appsettings.json**: Regular backup of configuration
- **Tokens.json**: Backup OAuth tokens (secure storage)
- **Templates**: Backup PDF templates and related files

### Database Backup
- **Regular Backups**: Ensure LEP database is regularly backed up
- **Recovery Testing**: Test backup recovery procedures
- **Point-in-Time Recovery**: Implement if required

### Disaster Recovery
- **Documentation**: Maintain deployment documentation
- **Recovery Procedures**: Document recovery steps
- **Testing**: Regular disaster recovery testing

## 📞 Support

### Log Analysis
- **Application Logs**: Check `logs/invoicer-{date}.txt` for detailed information
- **Database Logs**: Review `Invoicer2Log` table for processing history
- **System Logs**: Check Windows Event Logs for system-level issues

### Contact Information
- **Internal Support**: LEP IT Team
- **Application Issues**: Review documentation and logs
- **MYOB Issues**: MYOB Support Portal

---

**LEP Invoicer - Production Deployment Guide** 🚀
