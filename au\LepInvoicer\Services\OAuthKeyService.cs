using MYOB.AccountRight.SDK;
using MYOB.AccountRight.SDK.Contracts;
using Newtonsoft.Json;
using Microsoft.Extensions.Logging;

namespace LepInvoicer.Services;

/// <summary>
/// OAuth key service for persisting MYOB OAuth tokens
/// </summary>
public class OAuthKeyService : IOAuthKeyService
{
    private const string TokensFile = @"c:\myob\Tokens.json";
    private readonly ILogger<OAuthKeyService> _logger;
    private OAuthTokens? _tokens;

    public OAuthKeyService(ILogger<OAuthKeyService> logger)
    {
        _logger = logger;
        ReadFromFile();
    }

    /// <summary>
    /// OAuth response property that holds the tokens
    /// </summary>
    public OAuthTokens? OAuthResponse
    {
        get => _tokens;
        set
        {
            _tokens = value;
            SaveToFile();
        }
    }

    /// <summary>
    /// Read tokens from file
    /// </summary>
    private void ReadFromFile()
    {
        try
        {
            if (!File.Exists(TokensFile))
            {
                _logger.LogInformation("OAuth tokens file not found, will create new one");
                _tokens = null;
                return;
            }

            var json = File.ReadAllText(TokensFile);
            _tokens = JsonConvert.DeserializeObject<OAuthTokens>(json);
            _logger.LogInformation("OAuth tokens loaded from file");
        }
        catch (FileNotFoundException)
        {
            _logger.LogInformation("OAuth tokens file not found, will create new one");
            _tokens = null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to read OAuth tokens from file");
            _tokens = null;
        }
    }

    /// <summary>
    /// Save tokens to file
    /// </summary>
    private void SaveToFile()
    {
        try
        {
            // Ensure directory exists
            var directory = Path.GetDirectoryName(TokensFile);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            var json = JsonConvert.SerializeObject(_tokens, Formatting.Indented);
            File.WriteAllText(TokensFile, json);
            _logger.LogInformation("OAuth tokens saved to file");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save OAuth tokens to file");
        }
    }
}
